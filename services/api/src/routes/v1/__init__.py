"""API v1 routes."""

from fastapi import APIRouter
from . import files, workflow, vms, ecs, admin
from .. import health

# Create v1 router
v1_router = APIRouter(prefix="/api/v1")

# Include all v1 routes
v1_router.include_router(files.router, prefix="/files", tags=["files"])
v1_router.include_router(workflow.router, prefix="/workflow", tags=["workflow"])
v1_router.include_router(vms.router, prefix="/vms", tags=["vms"])
v1_router.include_router(ecs.router, prefix="/ecs", tags=["ecs"])
v1_router.include_router(admin.router, prefix="/admin", tags=["admin"])
v1_router.include_router(health.router, prefix="/health", tags=["health"])

__all__ = ["v1_router"]
