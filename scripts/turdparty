#!/bin/bash

# 💩🎉TurdParty🎉💩 - Sexy CLI Wrapper Script
# This script sets up the environment and runs the TurdParty CLI

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Get script directory and project root, handling symlinks correctly
SCRIPT_PATH="$(readlink -f "${BASH_SOURCE[0]}")"
SCRIPT_DIR="$(dirname "$SCRIPT_PATH")"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
CLI_SCRIPT="$SCRIPT_DIR/turdparty-cli.py"
REQUIREMENTS_FILE="$SCRIPT_DIR/requirements-cli.txt"

# Function to print colored output
print_colored() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Python dependencies (Nix-aware)
setup_dependencies() {
    print_colored $BLUE "🔍 Checking Python dependencies..."

    # Check if we're in a Nix shell
    if [ -n "${IN_NIX_SHELL:-}" ]; then
        print_colored $GREEN "✅ Running in Nix shell - dependencies managed by Nix"

        # Verify CLI dependencies are available
        local missing_deps=false

        # Check for click
        if ! python3 -c "import click" 2>/dev/null; then
            missing_deps=true
            print_colored $RED "❌ click not available in Nix shell"
        fi

        # Check for rich
        if ! python3 -c "import rich" 2>/dev/null; then
            missing_deps=true
            print_colored $RED "❌ rich not available in Nix shell"
        fi

        # Check for requests
        if ! python3 -c "import requests" 2>/dev/null; then
            missing_deps=true
            print_colored $RED "❌ requests not available in Nix shell"
        fi

        if [ "$missing_deps" = true ]; then
            print_colored $RED "❌ CLI dependencies missing from Nix shell!"
            print_colored $YELLOW "💡 Please update shell.nix to include:"
            print_colored $CYAN "   python312Packages.click"
            print_colored $CYAN "   python312Packages.rich"
            print_colored $CYAN "   python312Packages.requests"
            print_colored $YELLOW "💡 Then restart your Nix shell: exit && nix-shell"
            exit 1
        else
            print_colored $GREEN "✅ All CLI dependencies available in Nix shell"
        fi

        return 0
    fi

    # Not in Nix shell - suggest using Nix
    print_colored $YELLOW "⚠️  Not running in Nix shell"
    print_colored $CYAN "💡 For best experience, run: nix-shell"
    print_colored $CYAN "💡 This will provide all dependencies including the CLI tools"

    # Fallback: check if dependencies are available system-wide
    local missing_deps=false

    # Check for click
    if ! python3 -c "import click" 2>/dev/null; then
        missing_deps=true
    fi

    # Check for rich
    if ! python3 -c "import rich" 2>/dev/null; then
        missing_deps=true
    fi

    # Check for requests
    if ! python3 -c "import requests" 2>/dev/null; then
        missing_deps=true
    fi

    if [ "$missing_deps" = true ]; then
        print_colored $RED "❌ CLI dependencies not found!"
        print_colored $YELLOW "💡 Recommended: Use Nix shell for dependency management"
        print_colored $CYAN "   nix-shell"
        print_colored $YELLOW "💡 Alternative: Install manually with pip"
        print_colored $CYAN "   pip install click rich requests"
        exit 1
    else
        print_colored $GREEN "✅ All dependencies found system-wide"
    fi
}

# Function to show banner
show_banner() {
    print_colored $PURPLE "╔══════════════════════════════════════════════════════════════════════════════╗"
    print_colored $PURPLE "║                                                                              ║"
    print_colored $PURPLE "║                        💩🎉 TurdParty CLI 🎉💩                              ║"
    print_colored $PURPLE "║                                                                              ║"
    print_colored $PURPLE "║                    Malware Analysis Platform Manager                        ║"
    print_colored $PURPLE "║                                                                              ║"
    print_colored $PURPLE "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo ""
}

# Function to check if we're in the right directory
check_project_directory() {
    # Check if docker-compose.yml exists in project root (could be symlink)
    if [ ! -e "$PROJECT_ROOT/docker-compose.yml" ]; then
        print_colored $RED "❌ TurdParty project files not found!"
        print_colored $RED "   Expected docker-compose.yml in: $PROJECT_ROOT"
        print_colored $YELLOW "💡 Make sure you're running this from the TurdParty project directory"
        exit 1
    fi

    # Also check for other key files to confirm this is TurdParty
    if [ ! -d "$PROJECT_ROOT/services" ] || [ ! -f "$PROJECT_ROOT/shell.nix" ]; then
        print_colored $RED "❌ This doesn't appear to be a TurdParty project directory!"
        print_colored $RED "   Missing expected directories: services/, shell.nix"
        exit 1
    fi

    print_colored $GREEN "✅ TurdParty project directory validated: $PROJECT_ROOT"
}

# Function to check Python version
check_python() {
    if ! command_exists python3; then
        print_colored $RED "❌ Python 3 not found. Please install Python 3.8 or later."
        exit 1
    fi
    
    # Check Python version
    local python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    local major=$(echo $python_version | cut -d. -f1)
    local minor=$(echo $python_version | cut -d. -f2)
    
    if [ "$major" -lt 3 ] || ([ "$major" -eq 3 ] && [ "$minor" -lt 8 ]); then
        print_colored $RED "❌ Python 3.8 or later required. Found: $python_version"
        exit 1
    fi
    
    print_colored $GREEN "✅ Python $python_version detected"
}

# Main function
main() {
    # Check if --help or -h is passed
    if [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "-h" ]]; then
        show_banner
        print_colored $CYAN "TurdParty CLI - Beautiful command-line interface for managing TurdParty services"
        echo ""
        print_colored $YELLOW "Usage:"
        print_colored $WHITE "  turdparty [COMMAND] [OPTIONS]"
        echo ""
        print_colored $YELLOW "Available Commands:"
        print_colored $WHITE "  start      🚀 Start TurdParty services with dependency validation"
        print_colored $WHITE "  stop       🛑 Stop all TurdParty services"
        print_colored $WHITE "  status     📊 Show detailed service status"
        print_colored $WHITE "  monitor    📊 Real-time service monitoring dashboard"
        print_colored $WHITE "  check      🔍 Run comprehensive dependency and health checks"
        print_colored $WHITE "  logs       📋 Show service logs with filtering options"
        print_colored $WHITE "  restart    🔄 Restart a specific service"
        print_colored $WHITE "  rebuild    🏗️ Rebuild and restart all services"
        print_colored $WHITE "  test       🧪 Run the parallel test suite"
        print_colored $WHITE "  docs       📚 Documentation management and building"
        print_colored $WHITE "  admin      🔧 Administrative operations and system management"
        echo ""
        print_colored $YELLOW "Examples:"
        print_colored $CYAN "  turdparty start --logs --rebuild-docs    # Start with logs and rebuild docs"
        print_colored $CYAN "  turdparty start -i --no-animation        # Start with monitoring, skip animation"
        print_colored $CYAN "  turdparty monitor --filter-service api   # Monitor only API service"
        print_colored $CYAN "  turdparty logs api --level ERROR -f      # Follow API error logs"
        print_colored $CYAN "  turdparty docs --build --serve           # Build and serve documentation"
        print_colored $CYAN "  turdparty admin --service database       # Database admin operations"
        echo ""
        print_colored $YELLOW "For detailed help on any command:"
        print_colored $CYAN "  turdparty [COMMAND] --help"
        echo ""
        return 0
    fi
    
    # Show banner for interactive use
    if [ $# -eq 0 ]; then
        show_banner
    fi
    
    # Run checks
    check_project_directory
    check_python
    setup_dependencies
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Run the CLI
    python3 "$CLI_SCRIPT" "$@"
}

# Run main function with all arguments
main "$@"
