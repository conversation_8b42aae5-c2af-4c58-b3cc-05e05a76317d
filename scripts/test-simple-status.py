#!/usr/bin/env python3
"""
Test the simplified status messages (1 verb + 1 noun, with 2x2 every 10th time).
"""

import sys
import time
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# Import Rich components
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

def test_simplified_status_messages():
    """Test the simplified status message generation"""
    console.print("\n[bold magenta]🎉 Testing Simplified Status Messages! 🎉[/bold magenta]")
    
    # Create a simple test version of the function
    import random
    
    # Sample data for testing
    sample_verbs = [
        "Reticulating", "Bamboozling", "Accelerating", "Zombifying", "Photographically",
        "Discombobulating", "Flummoxing", "Mystifying", "Perplexing", "Stupefying"
    ]
    
    sample_nouns = [
        "💩 excrement", "🚽 toilet treasures", "💩 poopenfarten", "🎉 party particles", 
        "💩 memetrash", "🧻 bathroom bounties", "💩 scheißenhaufen", "🪠 plumbing presents",
        "💩 algorithmartefacts", "🎭 theatrical trash"
    ]
    
    def get_test_status_message():
        """Test version of the status message generator"""
        verb = random.choice(sample_verbs)
        noun = random.choice(sample_nouns)
        
        # Every 10th time (10% chance), use the elaborate 2x2 variant
        if random.randint(1, 10) == 10:
            # 2x2 variant: adjective + verb + adjective + noun
            adjectives = [
                "interdimensional", "quantum", "algorithmic", "distributed", 
                "containerized", "microservice", "cloud-native", "serverless", 
                "blockchain", "neural", "holographic", "cybernetic",
                "biomechanical", "nanotechnological", "extraterrestrial"
            ]
            
            adj1 = random.choice(adjectives)
            adj2 = random.choice(adjectives)
            
            # Make sure we don't repeat the same adjective
            while adj2 == adj1:
                adj2 = random.choice(adjectives)
                
            return f"{adj1} {verb.lower()} {adj2} {noun}...", True
        else:
            # Simple 1x1 variant: just verb + noun
            return f"{verb} {noun}...", False
    
    console.print("[bold yellow]🎭 Sample Status Messages (showing 20 examples):[/bold yellow]")
    
    simple_count = 0
    elaborate_count = 0
    
    for i in range(20):
        message, is_elaborate = get_test_status_message()
        
        if is_elaborate:
            elaborate_count += 1
            console.print(f"   {i+1:2d}. [bold cyan]{message}[/bold cyan] [dim](2x2 variant)[/dim]")
        else:
            simple_count += 1
            console.print(f"   {i+1:2d}. [dim cyan]{message}[/dim cyan] [dim](1x1 simple)[/dim]")
    
    console.print(f"\n[bold green]📊 Results:[/bold green]")
    console.print(f"   Simple (1x1): {simple_count}/20 ({simple_count/20*100:.0f}%)")
    console.print(f"   Elaborate (2x2): {elaborate_count}/20 ({elaborate_count/20*100:.0f}%)")
    console.print(f"   Expected: ~90% simple, ~10% elaborate")
    
    # Test with progress bar simulation
    console.print("\n[bold green]🚀 Testing with Simulated Progress Bar:[/bold green]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        task = progress.add_task("🚀 Starting services...", total=None)
        
        for i in range(8):
            message, is_elaborate = get_test_status_message()
            variant_type = "2x2" if is_elaborate else "1x1"
            
            progress.update(task, description=f"🚀 {message}")
            console.print(f"      [dim]({variant_type} variant)[/dim]")
            time.sleep(0.8)
    
    console.print("\n[green]✅ Simplified status message test completed![/green]")
    console.print("[dim]💩🎉 Much cleaner and more readable! 🎉💩[/dim]")

if __name__ == "__main__":
    test_simplified_status_messages()
