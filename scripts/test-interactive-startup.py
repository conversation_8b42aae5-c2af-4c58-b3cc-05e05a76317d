#!/usr/bin/env python3
"""
Test script for the interactive startup functionality.
This script tests the CLI without actually starting services.
"""

import sys
import subprocess
import time
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def test_cli_help():
    """Test that the CLI help shows the new interactive options"""
    print("🧪 Testing CLI help output...")
    
    try:
        result = subprocess.run(
            ["python3", "scripts/turdparty-cli.py", "start", "--help"],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            output = result.stdout
            
            # Check for new options
            checks = [
                "--interactive" in output,
                "Enter interactive monitoring mode" in output,
                "--monitor-interval" in output,
                "Monitoring refresh interval" in output
            ]
            
            if all(checks):
                print("✅ CLI help shows new interactive options correctly")
                return True
            else:
                print("❌ CLI help missing some interactive options")
                print(f"Output: {output}")
                return False
        else:
            print(f"❌ CLI help failed with exit code {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing CLI help: {e}")
        return False

def test_keyboard_handler():
    """Test that the KeyboardHandler class is properly defined in the CLI script"""
    print("🧪 Testing KeyboardHandler class...")

    try:
        # Read the CLI script and check for KeyboardHandler class
        cli_script_path = PROJECT_ROOT / "scripts" / "turdparty-cli.py"
        with open(cli_script_path, 'r') as f:
            content = f.read()

        # Check for KeyboardHandler class definition
        checks = [
            "class KeyboardHandler:" in content,
            "def __init__(self):" in content,
            "self.running = True" in content,
            "self.background_requested = False" in content,
            "def setup_terminal(self):" in content,
            "def restore_terminal(self):" in content,
            "def start_monitoring(self):" in content,
            "def check_input(self):" in content
        ]

        if all(checks):
            print("✅ KeyboardHandler class is properly defined")
            return True
        else:
            print("❌ KeyboardHandler class missing some required components")
            missing = [check for check, result in zip([
                "class KeyboardHandler:",
                "__init__ method",
                "running attribute",
                "background_requested attribute",
                "setup_terminal method",
                "restore_terminal method",
                "start_monitoring method",
                "check_input method"
            ], checks) if not result]
            print(f"Missing: {missing}")
            return False

    except Exception as e:
        print(f"❌ Error testing KeyboardHandler: {e}")
        return False

def test_syntax():
    """Test that the CLI script has valid Python syntax"""
    print("🧪 Testing Python syntax...")
    
    try:
        result = subprocess.run(
            ["python3", "-m", "py_compile", "scripts/turdparty-cli.py"],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Python syntax is valid")
            return True
        else:
            print(f"❌ Syntax error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing syntax: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Interactive Startup CLI Feature")
    print("=" * 50)
    
    tests = [
        test_syntax,
        test_cli_help,
        test_keyboard_handler
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Interactive startup feature is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
