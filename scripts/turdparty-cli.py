#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 - Sexy CLI Management Interface

A beautiful command-line interface for managing TurdParty malware analysis services.
Built with Click and Rich for a modern, interactive experience.
"""

import os
import sys
import time
import subprocess
import json
import threading
import signal
import select
import termios
import tty
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

import click
import requests
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.text import Text
from rich.align import Align
from rich.columns import Columns
from rich import box
from rich.prompt import Confirm, Prompt

# Initialize Rich console
console = Console()

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent.absolute()

class KeyboardHandler:
    """Handle keyboard input for interactive monitoring"""

    def __init__(self):
        self.running = True
        self.background_requested = False
        self.old_settings = None

    def setup_terminal(self):
        """Setup terminal for non-blocking input"""
        if sys.stdin.isatty():
            self.old_settings = termios.tcgetattr(sys.stdin)
            tty.setraw(sys.stdin.fileno())

    def restore_terminal(self):
        """Restore terminal settings"""
        if self.old_settings:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)

    def check_input(self):
        """Check for keyboard input in a separate thread"""
        while self.running:
            if sys.stdin.isatty() and select.select([sys.stdin], [], [], 0.1)[0]:
                try:
                    char = sys.stdin.read(1)
                    if char.lower() == 'b':
                        self.background_requested = True
                        self.running = False
                        break
                    elif char.lower() == 'q' or ord(char) == 3:  # q or Ctrl+C
                        self.running = False
                        break
                except:
                    break
            time.sleep(0.1)

    def start_monitoring(self):
        """Start keyboard monitoring in background thread"""
        self.setup_terminal()
        thread = threading.Thread(target=self.check_input, daemon=True)
        thread.start()
        return thread

class TurdPartyManager:
    """Main class for managing TurdParty services"""
    
    def __init__(self):
        self.console = console
        self.project_root = PROJECT_ROOT
        
    def run_script(self, script_name: str, args: List[str] = None) -> Tuple[int, str, str]:
        """Run a script and return exit code, stdout, stderr"""
        script_path = self.project_root / "scripts" / script_name
        if not script_path.exists():
            return 1, "", f"Script not found: {script_path}"
        
        cmd = ["bash", str(script_path)]
        if args:
            cmd.extend(args)
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return 1, "", "Script execution timed out"
        except Exception as e:
            return 1, "", str(e)
    
    def check_traefik_status(self) -> Dict[str, any]:
        """Check Traefik status and return detailed information"""
        try:
            # Check container
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=traefik", "--format", "{{.Names}}"],
                capture_output=True, text=True
            )
            container_running = "traefik" in result.stdout
            
            # Check API
            api_accessible = False
            service_count = 0
            try:
                response = requests.get("http://localhost:8080/api/overview", timeout=5)
                api_accessible = response.status_code == 200
                
                services_response = requests.get("http://localhost:8080/api/http/services", timeout=5)
                if services_response.status_code == 200:
                    service_count = len(services_response.json())
            except:
                pass
            
            return {
                "container_running": container_running,
                "api_accessible": api_accessible,
                "service_count": service_count,
                "status": "healthy" if container_running and api_accessible else "unhealthy"
            }
        except Exception as e:
            return {
                "container_running": False,
                "api_accessible": False,
                "service_count": 0,
                "status": "error",
                "error": str(e)
            }
    
    def get_service_status(self) -> Dict[str, Dict]:
        """Get status of all TurdParty services"""
        try:
            result = subprocess.run(
                ["docker-compose", "ps", "--format", "json"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            services = {}
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line:
                        try:
                            service_data = json.loads(line)
                            name = service_data.get('Service', 'unknown')
                            services[name] = {
                                "name": name,
                                "status": service_data.get('State', 'unknown'),
                                "health": service_data.get('Health', 'unknown'),
                                "ports": service_data.get('Publishers', [])
                            }
                        except json.JSONDecodeError:
                            continue
            
            return services
        except Exception as e:
            self.console.print(f"[red]Error getting service status: {e}[/red]")
            return {}

# Create manager instance
manager = TurdPartyManager()

@click.group(invoke_without_command=True)
@click.pass_context
def cli(ctx):
    """
    💩🎉TurdParty🎉💩 - Malware Analysis Platform CLI
    
    A beautiful command-line interface for managing TurdParty services.
    """
    if ctx.invoked_subcommand is None:
        show_main_dashboard()

def create_header() -> Panel:
    """Create the main header panel"""
    title = Text("💩🎉 TurdParty Management CLI 🎉💩", style="bold magenta")
    subtitle = Text("Malware Analysis Platform Control Center", style="dim cyan")
    
    header_content = Align.center(
        Text.assemble(title, "\n", subtitle)
    )
    
    return Panel(
        header_content,
        box=box.DOUBLE,
        style="bright_blue",
        padding=(1, 2)
    )

def create_traefik_panel() -> Panel:
    """Create Traefik status panel"""
    traefik_status = manager.check_traefik_status()
    
    if traefik_status["status"] == "healthy":
        status_text = Text("🟢 HEALTHY", style="bold green")
        details = f"Services: {traefik_status['service_count']} | API: ✅ | Container: ✅"
    elif traefik_status["status"] == "unhealthy":
        status_text = Text("🟡 DEGRADED", style="bold yellow")
        details = f"Container: {'✅' if traefik_status['container_running'] else '❌'} | API: {'✅' if traefik_status['api_accessible'] else '❌'}"
    else:
        status_text = Text("🔴 ERROR", style="bold red")
        details = f"Error: {traefik_status.get('error', 'Unknown')}"
    
    content = Text.assemble(
        "Status: ", status_text, "\n",
        details, style="dim"
    )
    
    return Panel(
        content,
        title="[bold]🌐 Traefik Proxy[/bold]",
        box=box.ROUNDED,
        style="blue"
    )

def create_services_table() -> Table:
    """Create services status table"""
    table = Table(
        title="🐳 Service Status",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold cyan"
    )
    
    table.add_column("Service", style="bold")
    table.add_column("Status", justify="center")
    table.add_column("Health", justify="center")
    table.add_column("Info", style="dim")
    
    services = manager.get_service_status()
    
    for service_name, service_data in services.items():
        status = service_data["status"]
        health = service_data["health"]
        
        # Status emoji and color
        if status == "running":
            status_display = "[green]🟢 Running[/green]"
        elif status == "exited":
            status_display = "[red]🔴 Stopped[/red]"
        else:
            status_display = f"[yellow]🟡 {status.title()}[/yellow]"
        
        # Health emoji
        if health == "healthy":
            health_display = "[green]✅[/green]"
        elif health == "unhealthy":
            health_display = "[red]❌[/red]"
        elif health == "starting":
            health_display = "[yellow]⏳[/yellow]"
        else:
            health_display = "[dim]➖[/dim]"
        
        # Service info
        ports = service_data.get("ports", [])
        info = f"Ports: {len(ports)}" if ports else "Internal"
        
        table.add_row(
            service_name,
            status_display,
            health_display,
            info
        )
    
    return table

def show_main_dashboard():
    """Show the main dashboard"""
    console.clear()
    
    # Create layout
    layout = Layout()
    layout.split_column(
        Layout(create_header(), size=5),
        Layout(name="main")
    )
    
    layout["main"].split_row(
        Layout(create_traefik_panel(), name="traefik"),
        Layout(create_services_table(), name="services")
    )
    
    console.print(layout)
    console.print("\n[dim]Use 'turdparty-cli --help' to see available commands[/dim]")

@cli.command()
@click.option('--logs', is_flag=True, help='Show logs after starting')
@click.option('--interactive', '-i', is_flag=True, help='Enter interactive monitoring mode after startup')
@click.option('--monitor-interval', default=5, help='Monitoring refresh interval in seconds (only with --interactive)')
@click.option('--skip-traefik-check', is_flag=True, help='Skip Traefik dependency validation')
@click.option('--timeout', default=300, help='Startup timeout in seconds')
@click.option('--parallel', is_flag=True, help='Start services in parallel (faster but less stable)')
@click.option('--rebuild-docs', is_flag=True, help='Rebuild documentation after startup')
@click.option('--no-animation', is_flag=True, help='Skip startup success animation')
def start(logs, interactive, monitor_interval, skip_traefik_check, timeout, parallel, rebuild_docs, no_animation):
    """🚀 Start TurdParty services with dependency validation"""
    console.clear()
    console.print(create_header())

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:

        # Check dependencies (unless skipped)
        if not skip_traefik_check:
            task1 = progress.add_task("🔍 Checking Traefik dependencies...", total=100)
            exit_code, stdout, stderr = manager.run_script("check-traefik-dependency.sh")

            if exit_code != 0:
                progress.update(task1, completed=100)
                console.print(Panel(
                    f"[red]❌ Dependency check failed![/red]\n\n{stderr}",
                    title="[red]Critical Error[/red]",
                    box=box.DOUBLE
                ))
                return

            progress.update(task1, completed=100)
        else:
            console.print("[yellow]⚠️ Skipping Traefik dependency check[/yellow]")

        # Start services with hilarious status updates
        task2 = progress.add_task("🚀 Starting TurdParty services...", total=100)
        args = []
        if logs:
            args.append("--logs")
        if skip_traefik_check:
            args.append("--skip-traefik-check")

        # Show funny status messages during startup
        _show_startup_status_messages(progress, task2)

        exit_code, stdout, stderr = manager.run_script("start-turdparty.sh", args)
        progress.update(task2, completed=100)

        if exit_code == 0:
            console.print(Panel(
                "[green]✅ TurdParty services started successfully![/green]",
                title="[green]Success[/green]",
                box=box.DOUBLE
            ))

            # Show success animation (unless disabled)
            if not no_animation:
                _show_startup_animation()

            # Show service URLs
            _display_service_urls()

            # Rebuild documentation if requested
            if rebuild_docs:
                console.print("\n[cyan]📚 Rebuilding documentation...[/cyan]")
                _rebuild_documentation()

            # Enter interactive monitoring mode if requested
            if interactive:
                console.print("\n[cyan]🔄 Entering interactive monitoring mode...[/cyan]")
                console.print("[dim]Press [B] to background, [Q] to quit, or Ctrl+C to exit[/dim]\n")
                time.sleep(2)  # Brief pause to let user read the message
                _start_interactive_monitoring(monitor_interval)

        else:
            console.print(Panel(
                f"[red]❌ Failed to start services![/red]\n\n{stderr}",
                title="[red]Error[/red]",
                box=box.DOUBLE
            ))

def _start_interactive_monitoring(interval):
    """Start interactive monitoring mode after container startup"""
    console.clear()

    # Create keyboard handler
    kb_handler = KeyboardHandler()

    def create_startup_dashboard():
        """Create the startup monitoring dashboard"""
        layout = Layout()
        layout.split_column(
            Layout(create_header(), size=5),
            Layout(name="main"),
            Layout(name="footer", size=4)
        )

        layout["main"].split_row(
            Layout(create_traefik_panel(), name="traefik"),
            Layout(create_services_table(), name="services")
        )

        # Add startup-specific footer with hotkey info
        timestamp = Text(f"Last updated: {datetime.now().strftime('%H:%M:%S')}", style="dim")
        hotkeys = Text("Hotkeys: [B]ackground | [Q]uit | Ctrl+C", style="dim cyan")
        status_msg = Text("🚀 Services started - Monitoring active", style="bold green")

        footer_content = Columns([
            Align.left(hotkeys),
            Align.center(status_msg),
            Align.right(timestamp)
        ])

        layout["footer"] = Layout(Panel(
            footer_content,
            box=box.ROUNDED,
            style="dim"
        ))

        return layout

    try:
        # Start keyboard monitoring
        kb_thread = kb_handler.start_monitoring()

        with Live(create_startup_dashboard(), refresh_per_second=1/interval, console=console) as live:
            while kb_handler.running:
                time.sleep(interval)
                if kb_handler.running:
                    live.update(create_startup_dashboard())

        # Check if backgrounding was requested
        if kb_handler.background_requested:
            console.print("\n[cyan]📱 Backgrounding startup monitor... Services continue running[/cyan]")
            console.print("[dim]Use 'turdparty monitor' to restart monitoring or 'turdparty status' for quick status[/dim]")

            # Create a background monitoring script for startup
            bg_script_path = PROJECT_ROOT / "scripts" / "startup-monitor-background.py"
            with open(bg_script_path, 'w') as f:
                f.write(f"""#!/usr/bin/env python3
import time
import subprocess
import sys
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent.absolute()

print("🔄 TurdParty Startup Monitor running in background...")
print("📊 Monitoring services every {interval} seconds")
print("🛑 Use 'pkill -f startup-monitor-background.py' to stop")

try:
    while True:
        # Check service status silently
        result = subprocess.run(
            ["docker-compose", "ps", "--quiet"],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            print("⚠️  Warning: Some services may be down")

        time.sleep({interval})

except KeyboardInterrupt:
    print("\\n🛑 Background startup monitor stopped")
    sys.exit(0)
""")

            # Make script executable
            bg_script_path.chmod(0o755)

            # Start background process
            subprocess.Popen([
                sys.executable, str(bg_script_path)
            ], start_new_session=True)

        else:
            console.print("\n[yellow]Interactive monitoring stopped by user[/yellow]")
            console.print("[dim]Services continue running in background[/dim]")

    except KeyboardInterrupt:
        console.print("\n[yellow]Interactive monitoring stopped by user[/yellow]")
        console.print("[dim]Services continue running in background[/dim]")
    finally:
        kb_handler.running = False
        kb_handler.restore_terminal()

def _show_startup_animation():
    """Show a fun ASCII animation with poop and party emojis"""
    console.print("\n[bold magenta]🎉 Startup Complete! 🎉[/bold magenta]")

    # Animation frames
    frames = [
        "💩    🎉    💩    🎉    💩",
        "🎉    💩    🎉    💩    🎉",
        "💩    🎉    💩    🎉    💩",
        "🎉    💩    🎉    💩    🎉",
        "💩🎉  TurdParty  🎉💩",
        "🎉💩  Ready!     💩🎉",
        "💩🎉  TurdParty  🎉💩",
        "🎉💩  Ready!     💩🎉"
    ]

    try:
        with console.status("[bold green]Celebrating startup success...") as status:
            for i in range(len(frames)):
                console.print(f"\r[bold yellow]{frames[i]}[/bold yellow]", end="")
                time.sleep(0.6)
                if i < len(frames) - 1:
                    console.print("\r" + " " * len(frames[i]), end="")

        console.print(f"\n[bold green]🚀 {frames[-2]} 🚀[/bold green]")
        time.sleep(1)
        console.print("[dim]Animation complete - services ready![/dim]\n")

    except KeyboardInterrupt:
        console.print("\n[dim]Animation skipped[/dim]")

def _rebuild_documentation():
    """Trigger documentation rebuild via API or script fallback"""
    try:
        # First try to trigger via API
        if _trigger_docs_rebuild_api():
            console.print("[green]✅ Documentation rebuild triggered via API[/green]")
            console.print("[dim]📖 Docs will be available at: http://frontend.turdparty.localhost/docs/[/dim]")
        else:
            # Fallback to script execution
            console.print("[yellow]⚠️ API unavailable, using script fallback[/yellow]")
            console.print("[cyan]📚 Building documentation...[/cyan]")

            exit_code, stdout, stderr = manager.run_script("build-docs-auto.sh", ["build"])

            if exit_code == 0:
                console.print("[green]✅ Documentation rebuilt successfully[/green]")

                # Try to extract API docs if API is running
                _extract_api_docs_startup()

                # Restart frontend to pick up new docs
                console.print("[cyan]🔄 Restarting frontend to serve updated docs...[/cyan]")
                restart_result = subprocess.run(
                    ["docker-compose", "restart", "frontend"],
                    cwd=manager.project_root,
                    capture_output=True,
                    text=True
                )

                if restart_result.returncode == 0:
                    console.print("[green]✅ Frontend restarted - updated docs available[/green]")
                    console.print("[dim]📖 Access docs at: http://frontend.turdparty.localhost/docs/[/dim]")
                else:
                    console.print("[yellow]⚠️ Frontend restart failed - docs may not be updated[/yellow]")

            else:
                console.print(f"[red]❌ Documentation rebuild failed: {stderr}[/red]")

    except Exception as e:
        console.print(f"[red]❌ Documentation rebuild error: {e}[/red]")

def _trigger_docs_rebuild_api():
    """Try to trigger documentation rebuild via API endpoint"""
    try:
        import requests

        # Try different API endpoints
        api_urls = [
            "http://api.turdparty.localhost/api/v1/admin/docs/rebuild",
            "http://localhost:8000/api/v1/admin/docs/rebuild"
        ]

        payload = {
            "clean": False,
            "extract_api": True,
            "restart_frontend": True
        }

        for url in api_urls:
            try:
                response = requests.post(url, json=payload, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    console.print(f"[cyan]📋 Task ID: {data.get('task_id', 'unknown')}[/cyan]")
                    return True
            except Exception as e:
                console.print(f"[dim]API attempt failed for {url}: {e}[/dim]")
                continue

        return False

    except ImportError:
        console.print("[yellow]⚠️ requests library not available for API calls[/yellow]")
        return False
    except Exception as e:
        console.print(f"[yellow]⚠️ API call failed: {e}[/yellow]")
        return False

def _extract_api_docs_startup():
    """Extract API documentation during startup if API is available"""
    try:
        import requests

        # Try to get OpenAPI spec from running API
        api_urls = [
            "http://api.turdparty.localhost/openapi.json",
            "http://localhost:8000/openapi.json"
        ]

        for url in api_urls:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    # Save to docs build directory
                    api_docs_path = PROJECT_ROOT / "docs" / "_build" / "html" / "api" / "openapi.json"
                    api_docs_path.parent.mkdir(parents=True, exist_ok=True)

                    with open(api_docs_path, 'w') as f:
                        f.write(response.text)

                    console.print("[green]✅ API documentation extracted and integrated[/green]")
                    return
            except:
                continue

        console.print("[dim]ℹ️ API not available for documentation extraction[/dim]")

    except ImportError:
        console.print("[dim]ℹ️ requests library not available for API extraction[/dim]")
    except Exception as e:
        console.print(f"[yellow]⚠️ API documentation extraction failed: {e}[/yellow]")

# Massive collection of hilarious status messages (500+ verbs, 300+ nouns)
RETICULATING_VERBS = [
    # A-D verbs (125 verbs)
    "Accelerating", "Accumulating", "Actualizing", "Administering", "Aggregating", "Amplifying", "Analyzing", "Annotating", "Anticipating", "Approximating",
    "Articulating", "Assembling", "Assimilating", "Authenticating", "Authorizing", "Automatizing", "Bifurcating", "Bootstrapping", "Broadcasting", "Buffering",
    "Calibrating", "Capitalizing", "Categorizing", "Centralizing", "Characterizing", "Choreographing", "Circumnavigating", "Circumventing", "Coagulating", "Collaborating",
    "Collating", "Commemorating", "Communicating", "Compensating", "Compiling", "Complementing", "Concatenating", "Concentrating", "Conceptualizing", "Concretizing",
    "Configuring", "Consolidating", "Constructing", "Contemplating", "Contextualizing", "Coordinating", "Correlating", "Crystallizing", "Culminating", "Cultivating",
    "Customizing", "Decelerating", "Decompressing", "Deconstructing", "Decrypting", "Defragmenting", "Deliberating", "Demonstrating", "Denominating", "Depolarizing",
    "Desynchronizing", "Determining", "Detonating", "Developing", "Differentiating", "Digitizing", "Discriminating", "Disseminating", "Distributing", "Diversifying",
    "Documenting", "Domesticating", "Dramatizing", "Duplicating", "Dynamizing", "Economizing", "Elaborating", "Electrifying", "Eliminating", "Emancipating",
    "Emphasizing", "Encapsulating", "Encouraging", "Energizing", "Engineering", "Enhancing", "Enumerating", "Equilibrating", "Establishing", "Estimating",
    "Evaluating", "Evaporating", "Excavating", "Exfoliating", "Exhilarating", "Expanding", "Expediting", "Experimenting", "Exploiting", "Exploring",
    "Exponentiating", "Extrapolating", "Fabricating", "Facilitating", "Fantasizing", "Fascinating", "Federating", "Fermenting", "Fertilizing", "Finalizing",
    "Fluctuating", "Formulating", "Fragmenting", "Franchising", "Galvanizing", "Generating", "Germinating", "Gesticulating", "Globalizing", "Gravitating",
    "Harmonizing", "Homogenizing", "Hypothesizing", "Idealizing", "Illuminating", "Implementing", "Improvising", "Inaugurating", "Incubating", "Industrializing",
    "Infiltrating", "Initializing", "Innovating", "Instantiating", "Integrating", "Intensifying", "Interpolating", "Investigating", "Invigorating", "Itemizing"
]

RETICULATING_VERBS_2 = [
    # J-R verbs (125 verbs)
    "Jeopardizing", "Juxtaposing", "Legitimizing", "Liberating", "Liquidating", "Localizing", "Lubricating", "Magnetizing", "Maintaining", "Manipulating",
    "Manufacturing", "Marginalizing", "Materializing", "Maximizing", "Mechanizing", "Mediating", "Metabolizing", "Metamorphosing", "Methodizing", "Minimizing",
    "Mobilizing", "Moderating", "Modernizing", "Modulating", "Monetizing", "Monopolizing", "Motivating", "Multiplying", "Navigating", "Necessitating",
    "Negotiating", "Neutralizing", "Normalizing", "Notarizing", "Objectifying", "Obliterating", "Optimizing", "Orchestrating", "Organizing", "Orientating",
    "Originating", "Oscillating", "Oxidizing", "Pacifying", "Parallelizing", "Parametrizing", "Participating", "Particularizing", "Pasteurizing", "Patronizing",
    "Penetrating", "Percolating", "Perpetuating", "Personalizing", "Philosophizing", "Photosynthesizing", "Plagiarizing", "Polarizing", "Politicizing", "Popularizing",
    "Precipitating", "Predetermining", "Predominating", "Prefabricating", "Pressurizing", "Prioritizing", "Privatizing", "Problematizing", "Procrastinating", "Proliferating",
    "Propagating", "Prophesying", "Proportioning", "Prosecuting", "Prototyping", "Publicizing", "Pulverizing", "Punctuating", "Purifying", "Quantifying",
    "Quarantining", "Questioning", "Randomizing", "Rationalizing", "Realizing", "Recalibrating", "Recapitulating", "Reciprocating", "Recognizing", "Recommending",
    "Reconciling", "Reconstructing", "Recuperating", "Redistributing", "Referencing", "Reformulating", "Refrigerating", "Regenerating", "Regurgitating", "Rehabilitating",
    "Rehydrating", "Reimbursing", "Reinforcing", "Rejuvenating", "Relativizing", "Relocating", "Remodeling", "Reorganizing", "Replenishing", "Representing",
    "Reprogramming", "Reproducing", "Requisitioning", "Researching", "Resynchronizing", "Reticulating", "Revolutionizing", "Rhapsodizing", "Romanticizing", "Routinizing"
]

RETICULATING_VERBS_3 = [
    # S-Z verbs (125 verbs)
    "Sanitizing", "Satirizing", "Satisfying", "Scandalizing", "Scrutinizing", "Secularizing", "Sedimenting", "Segmenting", "Sensationalizing", "Sequencing",
    "Serializing", "Simplifying", "Simulating", "Socializing", "Solemnizing", "Solidifying", "Specializing", "Specifying", "Speculating", "Spiritualizing",
    "Stabilizing", "Standardizing", "Sterilizing", "Stigmatizing", "Stimulating", "Strategizing", "Streamlining", "Structuring", "Subsidizing", "Substantiating",
    "Substituting", "Summarizing", "Supervising", "Supplementing", "Supplying", "Supporting", "Suppressing", "Surpassing", "Surrendering", "Surrounding",
    "Surveying", "Surviving", "Suspending", "Sustaining", "Symbolizing", "Sympathizing", "Synchronizing", "Syndicating", "Synthesizing", "Systematizing",
    "Tabulating", "Tantalizing", "Targeting", "Technologizing", "Temporizing", "Terrorizing", "Theorizing", "Therapeutizing", "Totalizing", "Tracking",
    "Transcending", "Transferring", "Transforming", "Transitioning", "Translating", "Transmitting", "Transplanting", "Transporting", "Traumatizing", "Triangulating",
    "Troubleshooting", "Tunneling", "Turbocharing", "Typifying", "Uncompressing", "Underestimating", "Understanding", "Undertaking", "Unifying", "Universalizing",
    "Unleashing", "Unpacking", "Unraveling", "Updating", "Upgrading", "Uploading", "Urbanizing", "Utilizing", "Vaccinating", "Validating",
    "Vandalizing", "Vaporizing", "Vectorizing", "Ventilating", "Verbalizing", "Verifying", "Versioning", "Victimizing", "Virtualizing", "Visualizing",
    "Vitalizing", "Vocalizing", "Volatilizing", "Volunteering", "Vulcanizing", "Weaponizing", "Westernizing", "Winterizing", "Witnessing", "Worshipping",
    "Xeroxing", "Yodeling", "Zealotizing", "Zigzagging", "Zombifying", "Zoning", "Zooming", "Zygotizing", "Systematically", "Methodically",
    "Algorithmically", "Heuristically", "Probabilistically", "Deterministically", "Stochastically", "Chaotically", "Fractally", "Exponentially", "Logarithmically", "Trigonometrically",
    "Geometrically", "Algebraically", "Statistically", "Dynamically", "Kinematically", "Thermodynamically", "Electromagnetically", "Quantumly", "Relativistically", "Cosmologically",
    "Microscopically", "Macroscopically", "Telescopically", "Spectroscopically", "Holographically", "Cryptographically", "Stenographically", "Choreographically", "Cinematographically", "Photographically"
]

RETICULATING_VERBS_4 = [
    # Additional creative verbs (125 verbs)
    "Bamboozling", "Befuddling", "Bewildering", "Baffling", "Boggling", "Confounding", "Discombobulating", "Flummoxing", "Mystifying", "Perplexing",
    "Stupefying", "Astounding", "Dumbfounding", "Gobsmacking", "Thunderstruck", "Flabbergasting", "Overwhelming", "Staggering", "Stunning", "Shocking",
    "Electrifying", "Galvanizing", "Energizing", "Invigorating", "Revitalizing", "Rejuvenating", "Refreshing", "Stimulating", "Exciting", "Thrilling",
    "Exhilarating", "Intoxicating", "Mesmerizing", "Hypnotizing", "Captivating", "Enchanting", "Bewitching", "Spellbinding", "Enthralling", "Fascinating",
    "Tantalizing", "Titillating", "Scintillating", "Dazzling", "Bedazzling", "Glittering", "Sparkling", "Shimmering", "Glistening", "Gleaming",
    "Radiating", "Emanating", "Exuding", "Oozing", "Seeping", "Permeating", "Infiltrating", "Penetrating", "Saturating", "Impregnating",
    "Infusing", "Suffusing", "Pervading", "Enveloping", "Encompassing", "Surrounding", "Encircling", "Embracing", "Hugging", "Cuddling",
    "Snuggling", "Nuzzling", "Caressing", "Stroking", "Petting", "Fondling", "Tickling", "Teasing", "Taunting", "Provoking",
    "Antagonizing", "Aggravating", "Irritating", "Annoying", "Vexing", "Irking", "Bothering", "Pestering", "Harassing", "Tormenting",
    "Torturing", "Agonizing", "Excruciating", "Devastating", "Demolishing", "Obliterating", "Annihilating", "Eradicating", "Exterminating", "Eliminating",
    "Liquidating", "Terminating", "Finishing", "Completing", "Concluding", "Finalizing", "Wrapping", "Sealing", "Closing", "Shutting",
    "Locking", "Securing", "Fastening", "Tightening", "Clenching", "Gripping", "Grasping", "Clutching", "Holding", "Maintaining",
    "Sustaining", "Supporting", "Upholding", "Reinforcing", "Strengthening", "Fortifying", "Solidifying", "Stabilizing", "Balancing", "Equilibrating"
]

POOP_VERBS = [
    # Classic poop verbs (60)
    "💩 excrementing", "💩 fecalizing", "💩 stooling", "💩 dunging", "💩 manuring", "💩 dropping", "💩 wasting", "💩 orduring", "💩 detriting", "💩 refusing",
    "💩 sewaging", "💩 sludging", "💩 mucking", "💩 filthing", "💩 griming", "💩 crudding", "💩 scumming", "💩 sliming", "💩 oozing", "💩 residuing",
    "💩 sedimenting", "💩 depositing", "💩 accumulating", "💩 collecting", "💩 gathering", "💩 assemblaging", "💩 conglomerating", "💩 agglomerating", "💩 concatenating", "💩 compiling",
    "💩 repositoring", "💩 stockpiling", "💩 reserving", "💩 caching", "💩 hoarding", "💩 stashing", "💩 bunkering", "💩 vaulting", "💩 archiving", "💩 databasing",
    "💩 registrying", "💩 cataloging", "💩 inventorying", "💩 manifesting", "💩 rostering", "💩 directorying", "💩 indexing", "💩 taxonomizing", "💩 classifying", "💩 categorizing",
    "💩 poopenfartening", "💩 scheißenhaufening", "💩 kotklumpening", "💩 stuhlsammlening", "💩 fäkalienformierening", "💩 exkrementexplodierening", "💩 dungdepotierening", "💩 mistmagnifizierening", "💩 abfallakkumulierening", "💩 schmutzspektakelning",

    # Funny compound verbs (60)
    "🚽 toilet-treasuring", "🧻 bathroom-bountying", "🪠 plumbing-presenting", "🚿 sewage-surprising", "🛁 bathtub-bonanzaing", "🧽 scrubbing-specimening", "🧴 cleaning-compounding", "🧼 soap-substituting", "🪣 bucket-bonusing", "🗑️ garbage-gifting",
    "📦 package-parceling", "📫 mailbox-mysterying", "📮 postbox-presenting", "📬 letterbox-looting", "📭 inbox-itemizing", "📪 outbox-offering", "📨 envelope-extraing", "📩 message-materializing", "📧 email-elementing", "💌 love-lettering",
    "🎁 gift-wrapping", "🎀 ribbon-residuing", "🎊 confetti-collecting", "🎉 party-particling", "🎈 balloon-bitting", "🎂 cake-crumbing", "🍰 dessert-debrising", "🧁 cupcake-componenting", "🍪 cookie-chunking", "🍩 donut-dropping",
    "🍕 pizza-piecing", "🍔 burger-bitting", "🌭 hotdog-hunking", "🥪 sandwich-scrapping", "🌮 taco-tidbitting", "🌯 burrito-bundling", "🥙 pita-portioning", "🍱 bento-boxing", "🍜 noodle-nuggeting", "🍝 pasta-particling",
    "🥗 salad-segmenting", "🥘 stew-sampling", "🍲 soup-specimening", "🍛 curry-chunking", "🍣 sushi-sectioning", "🍤 shrimp-sharding", "🦐 prawn-piecing", "🦀 crab-componenting", "🦞 lobster-lumping", "🐟 fish-fragmenting",
    "🔬 biological-byproducting", "🧪 chemical-compounding", "⚗️ laboratory-leftovering", "🧬 genetic-garbaging", "🦠 microbial-mattering", "🔬 cellular-componenting", "🧫 bacterial-bundling", "🦴 skeletal-specimening", "🫀 organic-offering", "🧠 neural-networking",

    # Technical/Scientific verbs (60)
    "💊 pharmaceutical-fragmenting", "💉 medical-materializing", "🩺 diagnostic-debrising", "🩹 bandage-bitting", "🏥 hospital-hazarding", "🚑 ambulance-artifacting", "⚕️ medical-mysterying", "🔬 scientific-sampling", "📊 statistical-specimening", "📈 graphical-garbaging",
    "💻 digital-debrising", "⌨️ keyboard-componenting", "🖱️ mouse-materializing", "🖥️ monitor-mattering", "📱 mobile-mucking", "💾 storage-specimening", "💿 disc-dropping", "📀 CD-collecting", "💽 floppy-fragmenting", "🖨️ printer-particling",
    "📠 fax-fragmenting", "☎️ telephone-tidbitting", "📞 phone-piecing", "📟 pager-particling", "📺 television-trashing", "📻 radio-refusing", "🎵 musical-mattering", "🎶 melodic-mucking", "🎼 notation-nuggeting", "🎹 piano-particling",
    "🎸 guitar-garbaging", "🥁 drum-debrising", "🎺 trumpet-trashing", "🎷 saxophone-scrapping", "🎻 violin-wasting", "🎤 microphone-mattering", "🎧 headphone-hunking", "🔊 speaker-specimening", "📢 megaphone-materializing", "📣 bullhorn-bitting",
    "🌟 stellar-specimening", "⭐ cosmic-collecting", "🌙 lunar-leaving", "☀️ solar-sampling", "🌍 planetary-particling", "🌌 galactic-garbaging", "🚀 rocket-refusing", "🛸 UFO-wasting", "👽 alien-artifacting", "🌠 meteor-mattering",
    "🏔️ mountain-materializing", "🌋 volcanic-wasting", "🏝️ island-itemizing", "🏖️ beach-bitting", "🌊 oceanic-offering", "🏞️ landscape-leaving", "🌲 forest-fragmenting", "🌳 tree-trashing", "🌿 plant-particling", "🌱 seedling-specimening",

    # Absurd/Creative verbs (60)
    "🦋 butterfly-bitting", "🐛 caterpillar-chunking", "🐜 ant-assemblaging", "🐝 bee-bundling", "🕷️ spider-specimening", "🦗 cricket-collecting", "🐞 ladybug-leaving", "🦟 mosquito-mattering", "🪰 fly-fragmenting", "🪲 beetle-bitting",
    "🐸 frog-fragmenting", "🐢 turtle-trashing", "🦎 lizard-leaving", "🐍 snake-specimening", "🐊 crocodile-chunking", "🦕 dinosaur-debrising", "🦖 T-Rex-trashing", "🐉 dragon-dropping", "🦄 unicorn-wasting", "🐲 mythical-mattering",
    "🎭 theatrical-trashing", "🎪 circus-specimening", "🎨 artistic-artifacting", "🖼️ painting-particling", "🎬 movie-mattering", "📽️ film-fragmenting", "🎞️ reel-refusing", "📸 photo-particling", "🖼️ picture-piecing", "🎯 target-trashing",
    "💩 Scheißenhaufening", "💩 Kotklumpening", "💩 Stuhlsammlening", "💩 Fäkalienformierening", "💩 Exkrementexplodierening", "💩 Dungdepotierening", "💩 Mistmagnifizierening", "💩 Abfallakkumulierening", "💩 Schmutzspektakelning", "💩 Dreckdynamiking",
    "🚽 Toilettentraumatizing", "🧻 Klopapierkatastrophing", "🪠 Rohrreinigungsresting", "🚿 Abwasseradventuring", "🛁 Badewannenballasting", "🧽 Schwammspecializing", "🧴 Reinigungsrückständing", "🧼 Seifensubstanzing", "🪣 Eimererlebnising", "🗑️ Müllmeisterwerking",
    "💩 Poopenfartening", "💩 Stoolenstrudeling", "💩 Crappencascading", "💩 Dungeondelighting", "💩 Excrementerprising", "💩 Fecalformationing", "💩 Wastewonderlanding", "💩 Sewagespectacling", "💩 Sludgeshowcasing", "💩 Muckmarveling",

    # Internet/Meme verbs (60)
    "💩 meme-trashing", "💩 viral-wasting", "💩 trending-turding", "💩 hashtag-hazarding", "💩 emoji-excrementing", "💩 gif-garbaging", "💩 video-voiding", "💩 streaming-slopping", "💩 podcast-pooping", "💩 blog-bowelmoving",
    "💩 tweet-turding", "💩 post-pooping", "💩 comment-crapping", "💩 reply-residuing", "💩 like-sliming", "💩 share-sludging", "💩 follower-fecalizing", "💩 subscriber-stooling", "💩 viewer-wasting", "💩 clickbait-crapping",
    "💩 algorithm-artifacting", "💩 data-dropping", "💩 metadata-mucking", "💩 cookie-crumbing", "💩 cache-cluttering", "💩 session-slopping", "💩 token-trashing", "💩 header-heaping", "💩 payload-pooping", "💩 response-wasting",
    "💩 request-residuing", "💩 server-stooling", "💩 client-crapping", "💩 database-dunging", "💩 query-quagmiring", "💩 indexing-insanitying", "💩 search-sliming", "💩 result-refusing", "💩 output-oozing", "💩 input-insanitying",
    "💩 processing-pooping", "💩 computing-crapping", "💩 calculation-crudding", "💩 operation-orduring", "💩 function-fecalizing", "💩 method-mucking", "💩 class-crapping", "💩 object-orduring", "💩 variable-wasting", "💩 constant-crudding",
    "💩 compilation-carnivaling", "💩 repository-reveling", "💩 stockpile-spectacling", "💩 reserve-rendezvousing", "💩 cache-celebrating", "💩 hoard-happening", "💩 stash-spectacling", "💩 bunker-bonanzaing", "💩 vault-vaudevilling", "💩 archive-arenaing"
]

def _get_random_status_message():
    """Generate a random hilarious status message"""
    import random

    # Combine all main verb lists for primary action
    main_verbs = RETICULATING_VERBS + RETICULATING_VERBS_2 + RETICULATING_VERBS_3 + RETICULATING_VERBS_4

    main_verb = random.choice(main_verbs)
    poop_verb_with_emoji = random.choice(POOP_VERBS)

    # Split emoji and verb text
    parts = poop_verb_with_emoji.split(' ', 1)
    if len(parts) == 2:
        emoji = parts[0]
        poop_verb = parts[1]
    else:
        emoji = "💩"
        poop_verb = poop_verb_with_emoji

    # Every 10th time (10% chance), use the elaborate 2x2 variant
    if random.randint(1, 10) == 10:
        # 2x2 variant: emoji + adjective + main_verb + adjective + poop_verb
        adjectives = [
            "interdimensional", "quantum", "algorithmic", "distributed",
            "containerized", "microservice", "cloud-native", "serverless",
            "blockchain", "neural", "holographic", "cybernetic",
            "biomechanical", "nanotechnological", "extraterrestrial"
        ]

        adj1 = random.choice(adjectives)
        adj2 = random.choice(adjectives)

        # Make sure we don't repeat the same adjective
        while adj2 == adj1:
            adj2 = random.choice(adjectives)

        return f"{emoji} {adj1} {main_verb.lower()} {adj2} {poop_verb}..."
    else:
        # Simple 1x1 variant: emoji + main_verb + poop_verb
        return f"{emoji} {main_verb} {poop_verb}..."

def _show_startup_status_messages(progress, task):
    """Show hilarious status messages during startup"""
    import threading
    import time

    def update_status():
        """Update status messages in a separate thread"""
        for i in range(8):  # Show 8 different messages
            if progress:
                message = _get_random_status_message()
                progress.update(task, description=f"🚀 {message}")
                time.sleep(0.8)  # Change message every 0.8 seconds

    # Start the status update thread
    status_thread = threading.Thread(target=update_status, daemon=True)
    status_thread.start()

def _display_service_urls():
    """Display clickable service URLs using centralized configuration"""
    try:
        # Import the service URL manager
        import sys
        sys.path.insert(0, str(PROJECT_ROOT / "utils"))
        from service_urls import ServiceURLManager

        # Get URL manager for development environment
        url_manager = ServiceURLManager('development')

        console.print("\n[bold cyan]🌐 Service URLs (Click to Open):[/bold cyan]")

        # Define services with descriptions and emojis
        service_info = {
            'frontend': ('🎨', 'Main Application Interface'),
            'api': ('🔌', 'REST API & Documentation'),
            'status': ('📊', 'System Status Dashboard'),
            'docs': ('📚', 'Documentation Portal'),
            'kibana': ('📈', 'Log Analytics & Monitoring'),
            'elasticsearch': ('🔍', 'Search & Data Storage'),
            'minio': ('🪣', 'File Storage & Management')
        }

        # Create clickable URLs table
        from rich.table import Table
        from rich.text import Text

        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Service", style="cyan", width=12)
        table.add_column("Description", style="white", width=25)
        table.add_column("URL", style="blue underline", width=35)
        table.add_column("Health", style="green", width=8)

        for service, (emoji, description) in service_info.items():
            try:
                service_url = url_manager.get_service_url(service)
                health_url = url_manager.get_service_url(service, include_health=True)

                # Create clickable URL text
                url_text = Text(service_url)
                url_text.stylize("link " + service_url)

                health_text = Text("🔗 Check")
                health_text.stylize("link " + health_url)

                table.add_row(
                    f"{emoji} {service}",
                    description,
                    url_text,
                    health_text
                )
            except Exception as e:
                table.add_row(
                    f"{emoji} {service}",
                    description,
                    f"[red]Config Error[/red]",
                    "[red]❌[/red]"
                )

        console.print(table)

        # Show API endpoints
        console.print("\n[bold yellow]🔌 Key API Endpoints:[/bold yellow]")
        try:
            api_base = url_manager.get_service_url('api')
            api_endpoints = [
                ('📁', 'File Upload', f"{api_base}/api/v1/files/upload"),
                ('🖥️', 'VM Management', f"{api_base}/api/v1/vms/"),
                ('📊', 'Health Check', f"{api_base}/health"),
                ('📖', 'OpenAPI Docs', f"{api_base}/docs"),
                ('🔧', 'Admin Panel', f"{api_base}/api/v1/admin/"),
            ]

            for emoji, name, url in api_endpoints:
                url_text = Text(url)
                url_text.stylize("link " + url)
                console.print(f"  {emoji} {name}: ", end="")
                console.print(url_text)

        except Exception as e:
            console.print(f"[red]❌ Could not load API endpoints: {e}[/red]")

        console.print("\n[dim]💡 Click any URL to open in your browser[/dim]")

    except ImportError as e:
        console.print(f"[red]❌ Could not import service URL manager: {e}[/red]")
    except Exception as e:
        console.print(f"[red]❌ Error displaying service URLs: {e}[/red]")

@cli.command()
def stop():
    """🛑 Stop all TurdParty services"""
    if not Confirm.ask("Are you sure you want to stop all TurdParty services?"):
        return
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        task = progress.add_task("🛑 Stopping services...", total=None)
        
        result = subprocess.run(
            ["docker-compose", "down"],
            cwd=manager.project_root,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            console.print("[green]✅ All services stopped successfully![/green]")
        else:
            console.print(f"[red]❌ Error stopping services: {result.stderr}[/red]")

@cli.command()
def status():
    """📊 Show detailed service status"""
    show_main_dashboard()

@cli.command()
def check():
    """🔍 Run comprehensive dependency and health checks"""
    console.clear()
    console.print(create_header())

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:

        task = progress.add_task("🔍 Running dependency checks...", total=None)
        exit_code, stdout, stderr = manager.run_script("check-traefik-dependency.sh")

        if exit_code == 0:
            console.print(Panel(
                "[green]✅ All dependency checks passed![/green]",
                title="[green]Health Check Results[/green]",
                box=box.DOUBLE
            ))
        else:
            console.print(Panel(
                f"[red]❌ Dependency checks failed![/red]\n\n{stderr}",
                title="[red]Health Check Results[/red]",
                box=box.DOUBLE
            ))

@cli.command()
@click.option('--interval', '-i', default=2, help='Refresh interval in seconds')
@click.option('--filter-service', '-s', help='Filter by specific service name')
@click.option('--filter-status', help='Filter by service status (healthy/unhealthy/degraded)')
@click.option('--output-format', '-o', type=click.Choice(['dashboard', 'json', 'table']), default='dashboard', help='Output format')
@click.option('--timeout', default=30, help='Service check timeout in seconds')
@click.option('--no-color', is_flag=True, help='Disable colored output')
@click.option('--compact', is_flag=True, help='Use compact display mode')
@click.option('--show-metrics', is_flag=True, help='Show detailed service metrics')
@click.option('--export-file', help='Export monitoring data to file')
def monitor(interval, filter_service, filter_status, output_format, timeout, no_color, compact, show_metrics, export_file):
    """📊 Real-time service monitoring dashboard"""
    console.clear()

    # Create keyboard handler
    kb_handler = KeyboardHandler()

    def create_live_dashboard():
        """Create the live monitoring dashboard"""
        layout = Layout()
        layout.split_column(
            Layout(create_header(), size=5),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )

        layout["main"].split_row(
            Layout(create_traefik_panel(), name="traefik"),
            Layout(create_services_table(), name="services")
        )

        # Add timestamp and hotkey info
        timestamp = Text(f"Last updated: {datetime.now().strftime('%H:%M:%S')}", style="dim")
        hotkeys = Text("Hotkeys: [B]ackground | [Q]uit | Ctrl+C", style="dim cyan")

        footer_content = Columns([
            Align.left(hotkeys),
            Align.right(timestamp)
        ])

        layout["footer"] = Layout(Panel(
            footer_content,
            box=box.ROUNDED,
            style="dim"
        ))

        return layout

    try:
        # Start keyboard monitoring
        kb_thread = kb_handler.start_monitoring()

        with Live(create_live_dashboard(), refresh_per_second=1/interval, console=console) as live:
            while kb_handler.running:
                time.sleep(interval)
                if kb_handler.running:
                    live.update(create_live_dashboard())

        # Check if backgrounding was requested
        if kb_handler.background_requested:
            console.print("\n[cyan]📱 Backgrounding monitor... Use 'fg' to return or 'turdparty monitor' to restart[/cyan]")

            # Create a background monitoring script
            bg_script_path = PROJECT_ROOT / "scripts" / "monitor-background.py"
            with open(bg_script_path, 'w') as f:
                f.write(f"""#!/usr/bin/env python3
import time
import subprocess
import sys
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent.absolute()

print("🔄 TurdParty Monitor running in background...")
print("📊 Monitoring services every {interval} seconds")
print("🛑 Use 'pkill -f monitor-background.py' to stop")

try:
    while True:
        # Check service status silently
        result = subprocess.run(
            ["docker-compose", "ps", "--quiet"],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            print("⚠️  Warning: Some services may be down")

        time.sleep({interval})

except KeyboardInterrupt:
    print("\\n🛑 Background monitor stopped")
    sys.exit(0)
""")

            # Make script executable
            bg_script_path.chmod(0o755)

            # Start background process
            subprocess.Popen([
                sys.executable, str(bg_script_path)
            ], start_new_session=True)

        else:
            console.print("\n[yellow]Monitoring stopped by user[/yellow]")

    except KeyboardInterrupt:
        console.print("\n[yellow]Monitoring stopped by user[/yellow]")
    finally:
        kb_handler.running = False
        kb_handler.restore_terminal()

@cli.command()
@click.argument('service_name')
@click.option('--follow', '-f', is_flag=True, help='Follow log output')
@click.option('--tail', '-n', default=50, help='Number of lines to show')
@click.option('--since', help='Show logs since timestamp (e.g., "2023-01-01T00:00:00Z")')
@click.option('--until', help='Show logs until timestamp')
@click.option('--grep', help='Filter logs by pattern (grep-style)')
@click.option('--level', type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']), help='Filter by log level')
@click.option('--output-file', help='Save logs to file')
@click.option('--json-format', is_flag=True, help='Output logs in JSON format')
@click.option('--no-timestamps', is_flag=True, help='Hide timestamps')
def logs(service_name, follow, tail, since, until, grep, level, output_file, json_format, no_timestamps):
    """📋 Show service logs"""
    console.print(f"[cyan]Showing logs for service: {service_name}[/cyan]")

    cmd = ["docker-compose", "logs"]
    if follow:
        cmd.append("-f")
    cmd.extend(["--tail", str(tail), service_name])

    try:
        subprocess.run(cmd, cwd=manager.project_root)
    except KeyboardInterrupt:
        console.print("\n[yellow]Log viewing stopped by user[/yellow]")

@cli.command()
@click.argument('service_name')
def restart(service_name):
    """🔄 Restart a specific service"""
    if not Confirm.ask(f"Are you sure you want to restart {service_name}?"):
        return

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:

        task = progress.add_task(f"🔄 Restarting {service_name}...", total=None)

        result = subprocess.run(
            ["docker-compose", "restart", service_name],
            cwd=manager.project_root,
            capture_output=True,
            text=True
        )

        if result.returncode == 0:
            console.print(f"[green]✅ {service_name} restarted successfully![/green]")
        else:
            console.print(f"[red]❌ Error restarting {service_name}: {result.stderr}[/red]")

@cli.command()
def rebuild():
    """🏗️ Rebuild and restart all services"""
    if not Confirm.ask("This will rebuild all services. Continue?"):
        return

    console.clear()
    console.print(create_header())

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:

        task = progress.add_task("🏗️ Rebuilding services...", total=100)
        exit_code, stdout, stderr = manager.run_script("rebuild-services.sh")
        progress.update(task, completed=100)

        if exit_code == 0:
            console.print(Panel(
                "[green]✅ Services rebuilt successfully![/green]",
                title="[green]Rebuild Complete[/green]",
                box=box.DOUBLE
            ))
        else:
            console.print(Panel(
                f"[red]❌ Rebuild failed![/red]\n\n{stderr}",
                title="[red]Rebuild Error[/red]",
                box=box.DOUBLE
            ))

@cli.command()
def test():
    """🧪 Run the parallel test suite"""
    console.clear()
    console.print(create_header())

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:

        task = progress.add_task("🧪 Running test suite...", total=None)
        exit_code, stdout, stderr = manager.run_script("run-parallel-tests.sh")

        if exit_code == 0:
            console.print(Panel(
                "[green]✅ All tests passed![/green]",
                title="[green]Test Results[/green]",
                box=box.DOUBLE
            ))
        else:
            console.print(Panel(
                f"[yellow]⚠️ Some tests failed![/yellow]\n\nCheck the output above for details.",
                title="[yellow]Test Results[/yellow]",
                box=box.DOUBLE
            ))

@cli.command()
@click.option('--build', is_flag=True, help='Build documentation')
@click.option('--watch', is_flag=True, help='Watch for changes and rebuild')
@click.option('--clean', is_flag=True, help='Clean build directory before building')
@click.option('--api-docs', is_flag=True, help='Extract API documentation')
@click.option('--serve', is_flag=True, help='Serve documentation locally')
@click.option('--port', default=8080, help='Port for local documentation server')
@click.option('--open-browser', is_flag=True, help='Open documentation in browser after building')
@click.option('--format', type=click.Choice(['html', 'pdf', 'epub']), default='html', help='Documentation format')
def docs(build, watch, clean, api_docs, serve, port, open_browser, format):
    """📚 Documentation management commands"""

    if not any([build, watch, clean, api_docs, serve]):
        # Default action - show documentation status
        _show_docs_status()
        return

    console.clear()
    console.print(create_header())

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:

        if clean:
            task = progress.add_task("🧹 Cleaning documentation build directory...", total=None)
            _clean_docs_build()

        if build or api_docs:
            task = progress.add_task("📚 Building documentation...", total=None)

            args = ["build"]
            if clean:
                args.append("--clean")

            exit_code, stdout, stderr = manager.run_script("build-docs-auto.sh", args)

            if exit_code == 0:
                console.print("[green]✅ Documentation built successfully![/green]")

                if api_docs:
                    console.print("[cyan]📡 Extracting API documentation...[/cyan]")
                    _extract_api_docs()

            else:
                console.print(f"[red]❌ Documentation build failed: {stderr}[/red]")
                return

        if watch:
            console.print("[cyan]👀 Starting documentation watch mode...[/cyan]")
            console.print("[dim]Press Ctrl+C to stop watching[/dim]")
            try:
                manager.run_script("build-docs-auto.sh", ["watch"])
            except KeyboardInterrupt:
                console.print("\n[yellow]Documentation watch stopped[/yellow]")

        if serve:
            console.print(f"[cyan]🌐 Starting documentation server on port {port}...[/cyan]")
            _serve_docs(port, open_browser)

def _show_docs_status():
    """Show current documentation status"""
    console.print(create_header())

    # Check if documentation exists
    docs_path = PROJECT_ROOT / "docs" / "_build" / "html"

    if docs_path.exists():
        console.print("[green]✅ Documentation build found[/green]")

        # Check last build time
        index_file = docs_path / "index.html"
        if index_file.exists():
            import os
            mtime = os.path.getmtime(index_file)
            build_time = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
            console.print(f"[dim]Last built: {build_time}[/dim]")

        # Show access URLs
        console.print("\n[cyan]📖 Access Documentation:[/cyan]")
        console.print("• Local file: file://" + str(index_file))
        console.print("• Frontend: http://frontend.turdparty.localhost/docs/")
        console.print("• API docs: http://api.turdparty.localhost/docs")

    else:
        console.print("[red]❌ Documentation not built[/red]")
        console.print("[yellow]💡 Run 'turdparty docs --build' to build documentation[/yellow]")

def _clean_docs_build():
    """Clean documentation build directory"""
    import shutil
    docs_build_path = PROJECT_ROOT / "docs" / "_build"
    if docs_build_path.exists():
        shutil.rmtree(docs_build_path)
        console.print("[green]✅ Documentation build directory cleaned[/green]")

def _extract_api_docs():
    """Extract API documentation from running service"""
    try:
        import requests

        api_urls = [
            "http://api.turdparty.localhost/openapi.json",
            "http://localhost:8000/openapi.json"
        ]

        for url in api_urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    api_docs_path = PROJECT_ROOT / "docs" / "_build" / "html" / "api" / "openapi.json"
                    api_docs_path.parent.mkdir(parents=True, exist_ok=True)

                    with open(api_docs_path, 'w') as f:
                        f.write(response.text)

                    console.print("[green]✅ API documentation extracted[/green]")
                    return
            except:
                continue

        console.print("[yellow]⚠️ Could not extract API documentation - service may not be running[/yellow]")

    except ImportError:
        console.print("[red]❌ requests library not available for API extraction[/red]")

def _serve_docs(port, open_browser):
    """Serve documentation locally"""
    docs_path = PROJECT_ROOT / "docs" / "_build" / "html"

    if not docs_path.exists():
        console.print("[red]❌ Documentation not built. Run --build first.[/red]")
        return

    try:
        import http.server
        import socketserver
        import webbrowser
        import os

        os.chdir(docs_path)

        with socketserver.TCPServer(("", port), http.server.SimpleHTTPRequestHandler) as httpd:
            console.print(f"[green]✅ Documentation server running at http://localhost:{port}[/green]")

            if open_browser:
                webbrowser.open(f"http://localhost:{port}")

            console.print("[dim]Press Ctrl+C to stop server[/dim]")
            httpd.serve_forever()

    except KeyboardInterrupt:
        console.print("\n[yellow]Documentation server stopped[/yellow]")
    except Exception as e:
        console.print(f"[red]❌ Failed to start documentation server: {e}[/red]")

@cli.command()
@click.option('--service', help='Target specific service for admin operations')
@click.option('--force', is_flag=True, help='Force operation without confirmation')
@click.option('--dry-run', is_flag=True, help='Show what would be done without executing')
@click.option('--backup', is_flag=True, help='Create backup before destructive operations')
def admin(service, force, dry_run, backup):
    """🔧 Administrative operations and system management"""

    console.clear()
    console.print(create_header())

    if not service:
        _show_admin_menu()
        return

    console.print(f"[cyan]🔧 Administrative operations for: {service}[/cyan]")

    if dry_run:
        console.print("[yellow]🔍 DRY RUN MODE - No changes will be made[/yellow]")

    # Show available admin operations
    admin_operations = {
        "api": ["restart", "rebuild", "logs", "health", "docs-rebuild"],
        "database": ["restart", "backup", "restore", "health"],
        "redis": ["restart", "flush", "health", "info"],
        "elasticsearch": ["restart", "health", "indices", "cleanup"],
        "frontend": ["restart", "rebuild", "health"],
        "all": ["restart", "rebuild", "health", "cleanup"]
    }

    if service in admin_operations:
        console.print(f"\n[yellow]Available operations for {service}:[/yellow]")
        for op in admin_operations[service]:
            console.print(f"  • {op}")
    else:
        console.print(f"[red]❌ Unknown service: {service}[/red]")
        console.print("[yellow]Available services: " + ", ".join(admin_operations.keys()) + "[/yellow]")

def _show_admin_menu():
    """Show interactive admin menu"""
    console.print("[bold cyan]🔧 TurdParty Administrative Dashboard[/bold cyan]\n")

    # System status overview
    console.print("[yellow]📊 System Status Overview:[/yellow]")

    # Quick health checks
    services = ["api", "database", "redis", "elasticsearch", "frontend"]
    for service in services:
        status = _quick_health_check(service)
        status_icon = "✅" if status == "healthy" else "❌" if status == "unhealthy" else "⚠️"
        console.print(f"  {status_icon} {service}: {status}")

    console.print("\n[yellow]🛠️ Available Administrative Operations:[/yellow]")
    console.print("  • System Health: turdparty admin --service all")
    console.print("  • Service Restart: turdparty admin --service <service>")
    console.print("  • Documentation Rebuild: turdparty docs --build")
    console.print("  • Database Operations: turdparty admin --service database")
    console.print("  • Log Analysis: turdparty logs <service> --level ERROR")

    console.print("\n[dim]Use 'turdparty admin --service <service>' for service-specific operations[/dim]")

def _quick_health_check(service):
    """Perform quick health check for a service"""
    try:
        import requests

        health_urls = {
            "api": "http://api.turdparty.localhost/health",
            "elasticsearch": "http://elasticsearch.turdparty.localhost/_cluster/health",
            "frontend": "http://frontend.turdparty.localhost/",
        }

        if service in health_urls:
            response = requests.get(health_urls[service], timeout=5)
            return "healthy" if response.status_code == 200 else "unhealthy"
        else:
            # For services without HTTP endpoints, check docker status
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name={service}", "--format", "{{.Status}}"],
                capture_output=True, text=True, timeout=5
            )
            return "healthy" if "Up" in result.stdout else "unhealthy"

    except:
        return "unknown"

if __name__ == "__main__":
    cli()
