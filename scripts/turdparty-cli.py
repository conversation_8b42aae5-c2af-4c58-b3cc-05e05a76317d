#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 - Sexy CLI Management Interface

A beautiful command-line interface for managing TurdParty malware analysis services.
Built with Click and Rich for a modern, interactive experience.
"""

import os
import sys
import time
import subprocess
import json
import threading
import signal
import select
import termios
import tty
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime

import click
import requests
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.text import Text
from rich.align import Align
from rich.columns import Columns
from rich import box
from rich.prompt import Confirm, Prompt

# Initialize Rich console
console = Console()

# Project root directory
PROJECT_ROOT = Path(__file__).parent.parent.absolute()

class KeyboardHandler:
    """Handle keyboard input for interactive monitoring"""

    def __init__(self):
        self.running = True
        self.background_requested = False
        self.old_settings = None

    def setup_terminal(self):
        """Setup terminal for non-blocking input"""
        if sys.stdin.isatty():
            self.old_settings = termios.tcgetattr(sys.stdin)
            tty.setraw(sys.stdin.fileno())

    def restore_terminal(self):
        """Restore terminal settings"""
        if self.old_settings:
            termios.tcsetattr(sys.stdin, termios.TCSADRAIN, self.old_settings)

    def check_input(self):
        """Check for keyboard input in a separate thread"""
        while self.running:
            if sys.stdin.isatty() and select.select([sys.stdin], [], [], 0.1)[0]:
                try:
                    char = sys.stdin.read(1)
                    if char.lower() == 'b':
                        self.background_requested = True
                        self.running = False
                        break
                    elif char.lower() == 'q' or ord(char) == 3:  # q or Ctrl+C
                        self.running = False
                        break
                except:
                    break
            time.sleep(0.1)

    def start_monitoring(self):
        """Start keyboard monitoring in background thread"""
        self.setup_terminal()
        thread = threading.Thread(target=self.check_input, daemon=True)
        thread.start()
        return thread

class TurdPartyManager:
    """Main class for managing TurdParty services"""
    
    def __init__(self):
        self.console = console
        self.project_root = PROJECT_ROOT
        
    def run_script(self, script_name: str, args: List[str] = None) -> Tuple[int, str, str]:
        """Run a script and return exit code, stdout, stderr"""
        script_path = self.project_root / "scripts" / script_name
        if not script_path.exists():
            return 1, "", f"Script not found: {script_path}"
        
        cmd = ["bash", str(script_path)]
        if args:
            cmd.extend(args)
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return 1, "", "Script execution timed out"
        except Exception as e:
            return 1, "", str(e)
    
    def check_traefik_status(self) -> Dict[str, any]:
        """Check Traefik status and return detailed information"""
        try:
            # Check container
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=traefik", "--format", "{{.Names}}"],
                capture_output=True, text=True
            )
            container_running = "traefik" in result.stdout
            
            # Check API
            api_accessible = False
            service_count = 0
            try:
                response = requests.get("http://localhost:8080/api/overview", timeout=5)
                api_accessible = response.status_code == 200
                
                services_response = requests.get("http://localhost:8080/api/http/services", timeout=5)
                if services_response.status_code == 200:
                    service_count = len(services_response.json())
            except:
                pass
            
            return {
                "container_running": container_running,
                "api_accessible": api_accessible,
                "service_count": service_count,
                "status": "healthy" if container_running and api_accessible else "unhealthy"
            }
        except Exception as e:
            return {
                "container_running": False,
                "api_accessible": False,
                "service_count": 0,
                "status": "error",
                "error": str(e)
            }
    
    def get_service_status(self) -> Dict[str, Dict]:
        """Get status of all TurdParty services"""
        try:
            result = subprocess.run(
                ["docker-compose", "ps", "--format", "json"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            services = {}
            if result.returncode == 0:
                for line in result.stdout.strip().split('\n'):
                    if line:
                        try:
                            service_data = json.loads(line)
                            name = service_data.get('Service', 'unknown')
                            services[name] = {
                                "name": name,
                                "status": service_data.get('State', 'unknown'),
                                "health": service_data.get('Health', 'unknown'),
                                "ports": service_data.get('Publishers', [])
                            }
                        except json.JSONDecodeError:
                            continue
            
            return services
        except Exception as e:
            self.console.print(f"[red]Error getting service status: {e}[/red]")
            return {}

# Create manager instance
manager = TurdPartyManager()

@click.group(invoke_without_command=True)
@click.pass_context
def cli(ctx):
    """
    💩🎉TurdParty🎉💩 - Malware Analysis Platform CLI
    
    A beautiful command-line interface for managing TurdParty services.
    """
    if ctx.invoked_subcommand is None:
        show_main_dashboard()

def create_header() -> Panel:
    """Create the main header panel"""
    title = Text("💩🎉 TurdParty Management CLI 🎉💩", style="bold magenta")
    subtitle = Text("Malware Analysis Platform Control Center", style="dim cyan")
    
    header_content = Align.center(
        Text.assemble(title, "\n", subtitle)
    )
    
    return Panel(
        header_content,
        box=box.DOUBLE,
        style="bright_blue",
        padding=(1, 2)
    )

def create_traefik_panel() -> Panel:
    """Create Traefik status panel"""
    traefik_status = manager.check_traefik_status()
    
    if traefik_status["status"] == "healthy":
        status_text = Text("🟢 HEALTHY", style="bold green")
        details = f"Services: {traefik_status['service_count']} | API: ✅ | Container: ✅"
    elif traefik_status["status"] == "unhealthy":
        status_text = Text("🟡 DEGRADED", style="bold yellow")
        details = f"Container: {'✅' if traefik_status['container_running'] else '❌'} | API: {'✅' if traefik_status['api_accessible'] else '❌'}"
    else:
        status_text = Text("🔴 ERROR", style="bold red")
        details = f"Error: {traefik_status.get('error', 'Unknown')}"
    
    content = Text.assemble(
        "Status: ", status_text, "\n",
        details, style="dim"
    )
    
    return Panel(
        content,
        title="[bold]🌐 Traefik Proxy[/bold]",
        box=box.ROUNDED,
        style="blue"
    )

def create_services_table() -> Table:
    """Create services status table"""
    table = Table(
        title="🐳 Service Status",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold cyan"
    )
    
    table.add_column("Service", style="bold")
    table.add_column("Status", justify="center")
    table.add_column("Health", justify="center")
    table.add_column("Info", style="dim")
    
    services = manager.get_service_status()
    
    for service_name, service_data in services.items():
        status = service_data["status"]
        health = service_data["health"]
        
        # Status emoji and color
        if status == "running":
            status_display = "[green]🟢 Running[/green]"
        elif status == "exited":
            status_display = "[red]🔴 Stopped[/red]"
        else:
            status_display = f"[yellow]🟡 {status.title()}[/yellow]"
        
        # Health emoji
        if health == "healthy":
            health_display = "[green]✅[/green]"
        elif health == "unhealthy":
            health_display = "[red]❌[/red]"
        elif health == "starting":
            health_display = "[yellow]⏳[/yellow]"
        else:
            health_display = "[dim]➖[/dim]"
        
        # Service info
        ports = service_data.get("ports", [])
        info = f"Ports: {len(ports)}" if ports else "Internal"
        
        table.add_row(
            service_name,
            status_display,
            health_display,
            info
        )
    
    return table

def show_main_dashboard():
    """Show the main dashboard"""
    console.clear()
    
    # Create layout
    layout = Layout()
    layout.split_column(
        Layout(create_header(), size=5),
        Layout(name="main")
    )
    
    layout["main"].split_row(
        Layout(create_traefik_panel(), name="traefik"),
        Layout(create_services_table(), name="services")
    )
    
    console.print(layout)
    console.print("\n[dim]Use 'turdparty-cli --help' to see available commands[/dim]")

@cli.command()
@click.option('--logs', is_flag=True, help='Show logs after starting')
@click.option('--interactive', '-i', is_flag=True, help='Enter interactive monitoring mode after startup')
@click.option('--monitor-interval', default=5, help='Monitoring refresh interval in seconds (only with --interactive)')
@click.option('--skip-traefik-check', is_flag=True, help='Skip Traefik dependency validation')
@click.option('--timeout', default=300, help='Startup timeout in seconds')
@click.option('--parallel', is_flag=True, help='Start services in parallel (faster but less stable)')
@click.option('--rebuild-docs', is_flag=True, help='Rebuild documentation after startup')
@click.option('--no-animation', is_flag=True, help='Skip startup success animation')
def start(logs, interactive, monitor_interval, skip_traefik_check, timeout, parallel, rebuild_docs, no_animation):
    """🚀 Start TurdParty services with dependency validation"""
    console.clear()
    console.print(create_header())

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:

        # Check dependencies (unless skipped)
        if not skip_traefik_check:
            task1 = progress.add_task("🔍 Checking Traefik dependencies...", total=100)
            exit_code, stdout, stderr = manager.run_script("check-traefik-dependency.sh")

            if exit_code != 0:
                progress.update(task1, completed=100)
                console.print(Panel(
                    f"[red]❌ Dependency check failed![/red]\n\n{stderr}",
                    title="[red]Critical Error[/red]",
                    box=box.DOUBLE
                ))
                return

            progress.update(task1, completed=100)
        else:
            console.print("[yellow]⚠️ Skipping Traefik dependency check[/yellow]")

        # Start services
        task2 = progress.add_task("🚀 Starting TurdParty services...", total=100)
        args = []
        if logs:
            args.append("--logs")
        if skip_traefik_check:
            args.append("--skip-traefik-check")

        exit_code, stdout, stderr = manager.run_script("start-turdparty.sh", args)
        progress.update(task2, completed=100)

        if exit_code == 0:
            console.print(Panel(
                "[green]✅ TurdParty services started successfully![/green]",
                title="[green]Success[/green]",
                box=box.DOUBLE
            ))

            # Show success animation (unless disabled)
            if not no_animation:
                _show_startup_animation()

            # Rebuild documentation if requested
            if rebuild_docs:
                console.print("\n[cyan]📚 Rebuilding documentation...[/cyan]")
                _rebuild_documentation()

            # Enter interactive monitoring mode if requested
            if interactive:
                console.print("\n[cyan]🔄 Entering interactive monitoring mode...[/cyan]")
                console.print("[dim]Press [B] to background, [Q] to quit, or Ctrl+C to exit[/dim]\n")
                time.sleep(2)  # Brief pause to let user read the message
                _start_interactive_monitoring(monitor_interval)

        else:
            console.print(Panel(
                f"[red]❌ Failed to start services![/red]\n\n{stderr}",
                title="[red]Error[/red]",
                box=box.DOUBLE
            ))

def _start_interactive_monitoring(interval):
    """Start interactive monitoring mode after container startup"""
    console.clear()

    # Create keyboard handler
    kb_handler = KeyboardHandler()

    def create_startup_dashboard():
        """Create the startup monitoring dashboard"""
        layout = Layout()
        layout.split_column(
            Layout(create_header(), size=5),
            Layout(name="main"),
            Layout(name="footer", size=4)
        )

        layout["main"].split_row(
            Layout(create_traefik_panel(), name="traefik"),
            Layout(create_services_table(), name="services")
        )

        # Add startup-specific footer with hotkey info
        timestamp = Text(f"Last updated: {datetime.now().strftime('%H:%M:%S')}", style="dim")
        hotkeys = Text("Hotkeys: [B]ackground | [Q]uit | Ctrl+C", style="dim cyan")
        status_msg = Text("🚀 Services started - Monitoring active", style="bold green")

        footer_content = Columns([
            Align.left(hotkeys),
            Align.center(status_msg),
            Align.right(timestamp)
        ])

        layout["footer"] = Layout(Panel(
            footer_content,
            box=box.ROUNDED,
            style="dim"
        ))

        return layout

    try:
        # Start keyboard monitoring
        kb_thread = kb_handler.start_monitoring()

        with Live(create_startup_dashboard(), refresh_per_second=1/interval, console=console) as live:
            while kb_handler.running:
                time.sleep(interval)
                if kb_handler.running:
                    live.update(create_startup_dashboard())

        # Check if backgrounding was requested
        if kb_handler.background_requested:
            console.print("\n[cyan]📱 Backgrounding startup monitor... Services continue running[/cyan]")
            console.print("[dim]Use 'turdparty monitor' to restart monitoring or 'turdparty status' for quick status[/dim]")

            # Create a background monitoring script for startup
            bg_script_path = PROJECT_ROOT / "scripts" / "startup-monitor-background.py"
            with open(bg_script_path, 'w') as f:
                f.write(f"""#!/usr/bin/env python3
import time
import subprocess
import sys
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent.absolute()

print("🔄 TurdParty Startup Monitor running in background...")
print("📊 Monitoring services every {interval} seconds")
print("🛑 Use 'pkill -f startup-monitor-background.py' to stop")

try:
    while True:
        # Check service status silently
        result = subprocess.run(
            ["docker-compose", "ps", "--quiet"],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            print("⚠️  Warning: Some services may be down")

        time.sleep({interval})

except KeyboardInterrupt:
    print("\\n🛑 Background startup monitor stopped")
    sys.exit(0)
""")

            # Make script executable
            bg_script_path.chmod(0o755)

            # Start background process
            subprocess.Popen([
                sys.executable, str(bg_script_path)
            ], start_new_session=True)

        else:
            console.print("\n[yellow]Interactive monitoring stopped by user[/yellow]")
            console.print("[dim]Services continue running in background[/dim]")

    except KeyboardInterrupt:
        console.print("\n[yellow]Interactive monitoring stopped by user[/yellow]")
        console.print("[dim]Services continue running in background[/dim]")
    finally:
        kb_handler.running = False
        kb_handler.restore_terminal()

def _show_startup_animation():
    """Show a fun ASCII animation with poop and party emojis"""
    console.print("\n[bold magenta]🎉 Startup Complete! 🎉[/bold magenta]")

    # Animation frames
    frames = [
        "💩    🎉    💩    🎉    💩",
        "🎉    💩    🎉    💩    🎉",
        "💩    🎉    💩    🎉    💩",
        "🎉    💩    🎉    💩    🎉",
        "💩🎉  TurdParty  🎉💩",
        "🎉💩  Ready!     💩🎉",
        "💩🎉  TurdParty  🎉💩",
        "🎉💩  Ready!     💩🎉"
    ]

    try:
        with console.status("[bold green]Celebrating startup success...") as status:
            for i in range(len(frames)):
                console.print(f"\r[bold yellow]{frames[i]}[/bold yellow]", end="")
                time.sleep(0.6)
                if i < len(frames) - 1:
                    console.print("\r" + " " * len(frames[i]), end="")

        console.print(f"\n[bold green]🚀 {frames[-2]} 🚀[/bold green]")
        time.sleep(1)
        console.print("[dim]Animation complete - services ready![/dim]\n")

    except KeyboardInterrupt:
        console.print("\n[dim]Animation skipped[/dim]")

def _rebuild_documentation():
    """Rebuild documentation using build scripts"""
    try:
        console.print("[cyan]📚 Building documentation...[/cyan]")

        # Execute documentation build script
        exit_code, stdout, stderr = manager.run_script("build-docs-auto.sh", ["build"])

        if exit_code == 0:
            console.print("[green]✅ Documentation rebuilt successfully[/green]")

            # Try to extract API docs if API is running
            _extract_api_docs_startup()

            # Restart frontend to pick up new docs
            console.print("[cyan]🔄 Restarting frontend to serve updated docs...[/cyan]")
            restart_result = subprocess.run(
                ["docker-compose", "restart", "frontend"],
                cwd=manager.project_root,
                capture_output=True,
                text=True
            )

            if restart_result.returncode == 0:
                console.print("[green]✅ Frontend restarted - updated docs available[/green]")
                console.print("[dim]📖 Access docs at: http://frontend.turdparty.localhost/docs/[/dim]")
            else:
                console.print("[yellow]⚠️ Frontend restart failed - docs may not be updated[/yellow]")

        else:
            console.print(f"[red]❌ Documentation rebuild failed: {stderr}[/red]")

    except Exception as e:
        console.print(f"[red]❌ Documentation rebuild error: {e}[/red]")

def _extract_api_docs_startup():
    """Extract API documentation during startup if API is available"""
    try:
        import requests

        # Try to get OpenAPI spec from running API
        api_urls = [
            "http://api.turdparty.localhost/openapi.json",
            "http://localhost:8000/openapi.json"
        ]

        for url in api_urls:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    # Save to docs build directory
                    api_docs_path = PROJECT_ROOT / "docs" / "_build" / "html" / "api" / "openapi.json"
                    api_docs_path.parent.mkdir(parents=True, exist_ok=True)

                    with open(api_docs_path, 'w') as f:
                        f.write(response.text)

                    console.print("[green]✅ API documentation extracted and integrated[/green]")
                    return
            except:
                continue

        console.print("[dim]ℹ️ API not available for documentation extraction[/dim]")

    except ImportError:
        console.print("[dim]ℹ️ requests library not available for API extraction[/dim]")
    except Exception as e:
        console.print(f"[yellow]⚠️ API documentation extraction failed: {e}[/yellow]")

@cli.command()
def stop():
    """🛑 Stop all TurdParty services"""
    if not Confirm.ask("Are you sure you want to stop all TurdParty services?"):
        return
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        task = progress.add_task("🛑 Stopping services...", total=None)
        
        result = subprocess.run(
            ["docker-compose", "down"],
            cwd=manager.project_root,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            console.print("[green]✅ All services stopped successfully![/green]")
        else:
            console.print(f"[red]❌ Error stopping services: {result.stderr}[/red]")

@cli.command()
def status():
    """📊 Show detailed service status"""
    show_main_dashboard()

@cli.command()
def check():
    """🔍 Run comprehensive dependency and health checks"""
    console.clear()
    console.print(create_header())

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:

        task = progress.add_task("🔍 Running dependency checks...", total=None)
        exit_code, stdout, stderr = manager.run_script("check-traefik-dependency.sh")

        if exit_code == 0:
            console.print(Panel(
                "[green]✅ All dependency checks passed![/green]",
                title="[green]Health Check Results[/green]",
                box=box.DOUBLE
            ))
        else:
            console.print(Panel(
                f"[red]❌ Dependency checks failed![/red]\n\n{stderr}",
                title="[red]Health Check Results[/red]",
                box=box.DOUBLE
            ))

@cli.command()
@click.option('--interval', '-i', default=2, help='Refresh interval in seconds')
@click.option('--filter-service', '-s', help='Filter by specific service name')
@click.option('--filter-status', help='Filter by service status (healthy/unhealthy/degraded)')
@click.option('--output-format', '-o', type=click.Choice(['dashboard', 'json', 'table']), default='dashboard', help='Output format')
@click.option('--timeout', default=30, help='Service check timeout in seconds')
@click.option('--no-color', is_flag=True, help='Disable colored output')
@click.option('--compact', is_flag=True, help='Use compact display mode')
@click.option('--show-metrics', is_flag=True, help='Show detailed service metrics')
@click.option('--export-file', help='Export monitoring data to file')
def monitor(interval, filter_service, filter_status, output_format, timeout, no_color, compact, show_metrics, export_file):
    """📊 Real-time service monitoring dashboard"""
    console.clear()

    # Create keyboard handler
    kb_handler = KeyboardHandler()

    def create_live_dashboard():
        """Create the live monitoring dashboard"""
        layout = Layout()
        layout.split_column(
            Layout(create_header(), size=5),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )

        layout["main"].split_row(
            Layout(create_traefik_panel(), name="traefik"),
            Layout(create_services_table(), name="services")
        )

        # Add timestamp and hotkey info
        timestamp = Text(f"Last updated: {datetime.now().strftime('%H:%M:%S')}", style="dim")
        hotkeys = Text("Hotkeys: [B]ackground | [Q]uit | Ctrl+C", style="dim cyan")

        footer_content = Columns([
            Align.left(hotkeys),
            Align.right(timestamp)
        ])

        layout["footer"] = Layout(Panel(
            footer_content,
            box=box.ROUNDED,
            style="dim"
        ))

        return layout

    try:
        # Start keyboard monitoring
        kb_thread = kb_handler.start_monitoring()

        with Live(create_live_dashboard(), refresh_per_second=1/interval, console=console) as live:
            while kb_handler.running:
                time.sleep(interval)
                if kb_handler.running:
                    live.update(create_live_dashboard())

        # Check if backgrounding was requested
        if kb_handler.background_requested:
            console.print("\n[cyan]📱 Backgrounding monitor... Use 'fg' to return or 'turdparty monitor' to restart[/cyan]")

            # Create a background monitoring script
            bg_script_path = PROJECT_ROOT / "scripts" / "monitor-background.py"
            with open(bg_script_path, 'w') as f:
                f.write(f"""#!/usr/bin/env python3
import time
import subprocess
import sys
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent.parent.absolute()

print("🔄 TurdParty Monitor running in background...")
print("📊 Monitoring services every {interval} seconds")
print("🛑 Use 'pkill -f monitor-background.py' to stop")

try:
    while True:
        # Check service status silently
        result = subprocess.run(
            ["docker-compose", "ps", "--quiet"],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            print("⚠️  Warning: Some services may be down")

        time.sleep({interval})

except KeyboardInterrupt:
    print("\\n🛑 Background monitor stopped")
    sys.exit(0)
""")

            # Make script executable
            bg_script_path.chmod(0o755)

            # Start background process
            subprocess.Popen([
                sys.executable, str(bg_script_path)
            ], start_new_session=True)

        else:
            console.print("\n[yellow]Monitoring stopped by user[/yellow]")

    except KeyboardInterrupt:
        console.print("\n[yellow]Monitoring stopped by user[/yellow]")
    finally:
        kb_handler.running = False
        kb_handler.restore_terminal()

@cli.command()
@click.argument('service_name')
@click.option('--follow', '-f', is_flag=True, help='Follow log output')
@click.option('--tail', '-n', default=50, help='Number of lines to show')
@click.option('--since', help='Show logs since timestamp (e.g., "2023-01-01T00:00:00Z")')
@click.option('--until', help='Show logs until timestamp')
@click.option('--grep', help='Filter logs by pattern (grep-style)')
@click.option('--level', type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']), help='Filter by log level')
@click.option('--output-file', help='Save logs to file')
@click.option('--json-format', is_flag=True, help='Output logs in JSON format')
@click.option('--no-timestamps', is_flag=True, help='Hide timestamps')
def logs(service_name, follow, tail, since, until, grep, level, output_file, json_format, no_timestamps):
    """📋 Show service logs"""
    console.print(f"[cyan]Showing logs for service: {service_name}[/cyan]")

    cmd = ["docker-compose", "logs"]
    if follow:
        cmd.append("-f")
    cmd.extend(["--tail", str(tail), service_name])

    try:
        subprocess.run(cmd, cwd=manager.project_root)
    except KeyboardInterrupt:
        console.print("\n[yellow]Log viewing stopped by user[/yellow]")

@cli.command()
@click.argument('service_name')
def restart(service_name):
    """🔄 Restart a specific service"""
    if not Confirm.ask(f"Are you sure you want to restart {service_name}?"):
        return

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:

        task = progress.add_task(f"🔄 Restarting {service_name}...", total=None)

        result = subprocess.run(
            ["docker-compose", "restart", service_name],
            cwd=manager.project_root,
            capture_output=True,
            text=True
        )

        if result.returncode == 0:
            console.print(f"[green]✅ {service_name} restarted successfully![/green]")
        else:
            console.print(f"[red]❌ Error restarting {service_name}: {result.stderr}[/red]")

@cli.command()
def rebuild():
    """🏗️ Rebuild and restart all services"""
    if not Confirm.ask("This will rebuild all services. Continue?"):
        return

    console.clear()
    console.print(create_header())

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:

        task = progress.add_task("🏗️ Rebuilding services...", total=100)
        exit_code, stdout, stderr = manager.run_script("rebuild-services.sh")
        progress.update(task, completed=100)

        if exit_code == 0:
            console.print(Panel(
                "[green]✅ Services rebuilt successfully![/green]",
                title="[green]Rebuild Complete[/green]",
                box=box.DOUBLE
            ))
        else:
            console.print(Panel(
                f"[red]❌ Rebuild failed![/red]\n\n{stderr}",
                title="[red]Rebuild Error[/red]",
                box=box.DOUBLE
            ))

@cli.command()
def test():
    """🧪 Run the parallel test suite"""
    console.clear()
    console.print(create_header())

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:

        task = progress.add_task("🧪 Running test suite...", total=None)
        exit_code, stdout, stderr = manager.run_script("run-parallel-tests.sh")

        if exit_code == 0:
            console.print(Panel(
                "[green]✅ All tests passed![/green]",
                title="[green]Test Results[/green]",
                box=box.DOUBLE
            ))
        else:
            console.print(Panel(
                f"[yellow]⚠️ Some tests failed![/yellow]\n\nCheck the output above for details.",
                title="[yellow]Test Results[/yellow]",
                box=box.DOUBLE
            ))

@cli.command()
@click.option('--build', is_flag=True, help='Build documentation')
@click.option('--watch', is_flag=True, help='Watch for changes and rebuild')
@click.option('--clean', is_flag=True, help='Clean build directory before building')
@click.option('--api-docs', is_flag=True, help='Extract API documentation')
@click.option('--serve', is_flag=True, help='Serve documentation locally')
@click.option('--port', default=8080, help='Port for local documentation server')
@click.option('--open-browser', is_flag=True, help='Open documentation in browser after building')
@click.option('--format', type=click.Choice(['html', 'pdf', 'epub']), default='html', help='Documentation format')
def docs(build, watch, clean, api_docs, serve, port, open_browser, format):
    """📚 Documentation management commands"""

    if not any([build, watch, clean, api_docs, serve]):
        # Default action - show documentation status
        _show_docs_status()
        return

    console.clear()
    console.print(create_header())

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:

        if clean:
            task = progress.add_task("🧹 Cleaning documentation build directory...", total=None)
            _clean_docs_build()

        if build or api_docs:
            task = progress.add_task("📚 Building documentation...", total=None)

            args = ["build"]
            if clean:
                args.append("--clean")

            exit_code, stdout, stderr = manager.run_script("build-docs-auto.sh", args)

            if exit_code == 0:
                console.print("[green]✅ Documentation built successfully![/green]")

                if api_docs:
                    console.print("[cyan]📡 Extracting API documentation...[/cyan]")
                    _extract_api_docs()

            else:
                console.print(f"[red]❌ Documentation build failed: {stderr}[/red]")
                return

        if watch:
            console.print("[cyan]👀 Starting documentation watch mode...[/cyan]")
            console.print("[dim]Press Ctrl+C to stop watching[/dim]")
            try:
                manager.run_script("build-docs-auto.sh", ["watch"])
            except KeyboardInterrupt:
                console.print("\n[yellow]Documentation watch stopped[/yellow]")

        if serve:
            console.print(f"[cyan]🌐 Starting documentation server on port {port}...[/cyan]")
            _serve_docs(port, open_browser)

def _show_docs_status():
    """Show current documentation status"""
    console.print(create_header())

    # Check if documentation exists
    docs_path = PROJECT_ROOT / "docs" / "_build" / "html"

    if docs_path.exists():
        console.print("[green]✅ Documentation build found[/green]")

        # Check last build time
        index_file = docs_path / "index.html"
        if index_file.exists():
            import os
            mtime = os.path.getmtime(index_file)
            build_time = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
            console.print(f"[dim]Last built: {build_time}[/dim]")

        # Show access URLs
        console.print("\n[cyan]📖 Access Documentation:[/cyan]")
        console.print("• Local file: file://" + str(index_file))
        console.print("• Frontend: http://frontend.turdparty.localhost/docs/")
        console.print("• API docs: http://api.turdparty.localhost/docs")

    else:
        console.print("[red]❌ Documentation not built[/red]")
        console.print("[yellow]💡 Run 'turdparty docs --build' to build documentation[/yellow]")

def _clean_docs_build():
    """Clean documentation build directory"""
    import shutil
    docs_build_path = PROJECT_ROOT / "docs" / "_build"
    if docs_build_path.exists():
        shutil.rmtree(docs_build_path)
        console.print("[green]✅ Documentation build directory cleaned[/green]")

def _extract_api_docs():
    """Extract API documentation from running service"""
    try:
        import requests

        api_urls = [
            "http://api.turdparty.localhost/openapi.json",
            "http://localhost:8000/openapi.json"
        ]

        for url in api_urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    api_docs_path = PROJECT_ROOT / "docs" / "_build" / "html" / "api" / "openapi.json"
                    api_docs_path.parent.mkdir(parents=True, exist_ok=True)

                    with open(api_docs_path, 'w') as f:
                        f.write(response.text)

                    console.print("[green]✅ API documentation extracted[/green]")
                    return
            except:
                continue

        console.print("[yellow]⚠️ Could not extract API documentation - service may not be running[/yellow]")

    except ImportError:
        console.print("[red]❌ requests library not available for API extraction[/red]")

def _serve_docs(port, open_browser):
    """Serve documentation locally"""
    docs_path = PROJECT_ROOT / "docs" / "_build" / "html"

    if not docs_path.exists():
        console.print("[red]❌ Documentation not built. Run --build first.[/red]")
        return

    try:
        import http.server
        import socketserver
        import webbrowser
        import os

        os.chdir(docs_path)

        with socketserver.TCPServer(("", port), http.server.SimpleHTTPRequestHandler) as httpd:
            console.print(f"[green]✅ Documentation server running at http://localhost:{port}[/green]")

            if open_browser:
                webbrowser.open(f"http://localhost:{port}")

            console.print("[dim]Press Ctrl+C to stop server[/dim]")
            httpd.serve_forever()

    except KeyboardInterrupt:
        console.print("\n[yellow]Documentation server stopped[/yellow]")
    except Exception as e:
        console.print(f"[red]❌ Failed to start documentation server: {e}[/red]")

@cli.command()
@click.option('--service', help='Target specific service for admin operations')
@click.option('--force', is_flag=True, help='Force operation without confirmation')
@click.option('--dry-run', is_flag=True, help='Show what would be done without executing')
@click.option('--backup', is_flag=True, help='Create backup before destructive operations')
def admin(service, force, dry_run, backup):
    """🔧 Administrative operations and system management"""

    console.clear()
    console.print(create_header())

    if not service:
        _show_admin_menu()
        return

    console.print(f"[cyan]🔧 Administrative operations for: {service}[/cyan]")

    if dry_run:
        console.print("[yellow]🔍 DRY RUN MODE - No changes will be made[/yellow]")

    # Show available admin operations
    admin_operations = {
        "api": ["restart", "rebuild", "logs", "health", "docs-rebuild"],
        "database": ["restart", "backup", "restore", "health"],
        "redis": ["restart", "flush", "health", "info"],
        "elasticsearch": ["restart", "health", "indices", "cleanup"],
        "frontend": ["restart", "rebuild", "health"],
        "all": ["restart", "rebuild", "health", "cleanup"]
    }

    if service in admin_operations:
        console.print(f"\n[yellow]Available operations for {service}:[/yellow]")
        for op in admin_operations[service]:
            console.print(f"  • {op}")
    else:
        console.print(f"[red]❌ Unknown service: {service}[/red]")
        console.print("[yellow]Available services: " + ", ".join(admin_operations.keys()) + "[/yellow]")

def _show_admin_menu():
    """Show interactive admin menu"""
    console.print("[bold cyan]🔧 TurdParty Administrative Dashboard[/bold cyan]\n")

    # System status overview
    console.print("[yellow]📊 System Status Overview:[/yellow]")

    # Quick health checks
    services = ["api", "database", "redis", "elasticsearch", "frontend"]
    for service in services:
        status = _quick_health_check(service)
        status_icon = "✅" if status == "healthy" else "❌" if status == "unhealthy" else "⚠️"
        console.print(f"  {status_icon} {service}: {status}")

    console.print("\n[yellow]🛠️ Available Administrative Operations:[/yellow]")
    console.print("  • System Health: turdparty admin --service all")
    console.print("  • Service Restart: turdparty admin --service <service>")
    console.print("  • Documentation Rebuild: turdparty docs --build")
    console.print("  • Database Operations: turdparty admin --service database")
    console.print("  • Log Analysis: turdparty logs <service> --level ERROR")

    console.print("\n[dim]Use 'turdparty admin --service <service>' for service-specific operations[/dim]")

def _quick_health_check(service):
    """Perform quick health check for a service"""
    try:
        import requests

        health_urls = {
            "api": "http://api.turdparty.localhost/health",
            "elasticsearch": "http://elasticsearch.turdparty.localhost/_cluster/health",
            "frontend": "http://frontend.turdparty.localhost/",
        }

        if service in health_urls:
            response = requests.get(health_urls[service], timeout=5)
            return "healthy" if response.status_code == 200 else "unhealthy"
        else:
            # For services without HTTP endpoints, check docker status
            result = subprocess.run(
                ["docker", "ps", "--filter", f"name={service}", "--format", "{{.Status}}"],
                capture_output=True, text=True, timeout=5
            )
            return "healthy" if "Up" in result.stdout else "unhealthy"

    except:
        return "unknown"

if __name__ == "__main__":
    cli()
